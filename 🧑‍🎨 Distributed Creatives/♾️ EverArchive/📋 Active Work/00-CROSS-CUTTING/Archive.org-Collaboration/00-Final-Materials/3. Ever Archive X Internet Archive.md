**Open Letter to <PERSON>**  
*Distributed Creatives Conference – 10 July 2025*

---

Dear <PERSON>,

Over the past year, my team and I have been exploring a single, urgent question: **How do we ensure that humanity's creative and intellectual record survives—and remains verifiable—for centuries to come?**  That inquiry led us to design **EverArchive**, a complement to the Internet Archive that focuses on provenance-rich, creator-governed preservation.

You'll find fuller detail in the section "Why We're Building EverArchive" below.

The pages that follow are offered as a **tribute to the Internet Archive's trail-blazing work** and an **invitation to collaborate** on the next preservation layer.  I'd value your feedback and, if the vision resonates, your guidance on connecting with likeminded allies.

---

# EverArchive × Internet Archive

---

## 1  Opening Respect & Shared Mission

> "Brewster, the Internet Archive proved the **web can have a memory**.  EverArchive wants to ensure the **creators themselves – and the rich context around each work – are equally preserved**.  Our entire model stands on the shoulders of the IA ethos: open knowledge, public-good stewardship, and longevity measured in centuries, not quarters."

---

## 2  Why We're Building EverArchive

**Core Mission**: Preserve humanity's creative process—not just the final products—with verifiable provenance that lasts centuries.

**Three Pillars**:
1. **Deep-Time Memory** – Multi-chain storage with DAO-backed endowment ensures 100+ year preservation
2. **Creator Sovereignty** – Cryptographic provenance proves who made what, when, under which terms
3. **Living Archives** – Captures drafts, iterations, and the human story behind each work

**Who We Serve**: Artists, scientists, writers, developers, cultural institutions—anyone whose creative process deserves preservation alongside their finished works.

**Sustainability Model**: Community-owned DAO with diversified funding (no ads, no paywalls)—designed to complement Internet Archive and other preservation initiatives.

---

## 3  What's the Core Difference?

| Dimension | Internet Archive | EverArchive |
|-----------|------------------|-------------|
| **Primary focus** | Library of finished public artifacts (web pages, books, media) | Archive of creative **intent & provenance** (drafts, source files, licensing, process) |
| **Governance** | 501(c)(3) non-profit library | Creator- & community-owned DAO |
| **Storage fabric** | IA servers, S3, in-house tape | Chia Data Layer + Filecoin + institutional mirrors |
| **Revenue model** | Grants & donations | Tokenized endowment + grants + shared fundraising |
| **Status of objects** | Immutable snapshots | **Living** Deep-Authorship Objects (continuous versioning & rights updates) |

**One-liner** →
> *Internet Archive preserves finished cultural works—web pages, books, audio, video, software—making them publicly accessible; EverArchive preserves the **full creative lifecycle** behind those works, from first sketches to final release, adding creator-governed provenance, licensing, and process context. Where scopes overlap, we collaborate rather than duplicate.*

---

## 4  How EverArchive Complements the Internet Archive

The Internet Archive has pioneered large-scale preservation of public artifacts.  EverArchive focuses on **adjacent layers** that can feed richer context back into IA's collections.

1. **Draft & Process Materials** – EverArchive specialises in early drafts, private source files, and creative workflows, complementing IA's emphasis on public-facing finished works.
2. **Rights & Licensing Metadata** – We capture complex licensing and derivative-rights data at ingest, packaging it so IA (or others) can display it alongside the artifact.
3. **Emerging Media Formats** – EverArchive experiments with interactive code, VR scenes, and generative pipelines, providing prototypes IA can optionally mirror.
4. **Creator-Centric Governance** – Our DAO enables on-chain multi-party custody and revenue-sharing mechanisms that may inform future IA initiatives.

---

## 5  How We Complement Internet Archive

**Simple Architecture**:
```
CREATOR → EverArchive → Internet Archive → RESEARCHERS
         (process)      (finished work)     (complete story)
```

**Key Extensions**:
1. **Provenance Feed** – Cryptographic proof of authorship flows to IA's metadata
2. **Process Preservation** – We capture what happens before publication
3. **Creator Governance** – DAO model ensures long-term community stewardship
4. **Technical Bridges** – Open APIs and standards for seamless integration

## 6  EverArchive Tooling Stack  
*A quick tour of the software suite that makes the user journeys possible.*

### 5.1  Core Components

| Tool | Purpose | Current Status |
|------|---------|----------------|
| **EverPortal** (web) | Drag-and-drop onboarding for creators: uploads, rights templates, provenance preview. | MVP live on everarchive.org (v0.9) |
| **EverCLI** | Command-line interface for batch ingest, CI pipelines, and power users. Generates manifests locally before pushing. | Alpha binaries (macOS/Linux) |
| **EverNode** | Storage node that replicates encrypted shards across Chia Data Layer & Filecoin; publishes custody attestations. | Internal testnet (20 nodes) |
| **EverSDK** | JS/Python libraries + GraphQL endpoint for third-party integrations (museums, CMS, game engines). | Docs drafted; public beta July 2025 |
| **EverScan** | Explorer & audit dashboard: verify signatures, lineage graphs, storage health. | Design complete; engineering underway |
| **EverMint** | Schema.org-driven minting tool for composable NFT media (Chia NFT1 standard). | Prototype demo live on testnet |

> *Track record:* our team previously built open-source tooling for the Chia ecosystem (e.g., `chia-datalayer-utils`, `chia-offerbot`).  We're re-using that experience to harden EverNode and EverCLI.

### 5.3  Extensibility Roadmap
1. **Plugin framework** – allow institutions to write ingest adapters (e.g., OAI-PMH, Fedora Commons, ArchivesSpace).
2. **IPFS / Arweave bridges** – optional redundancy layers.
3. **ORCID & Wikidata hooks** – automatic authority linking for creator identities.

> **Why this matters for Brewster:** These tools ensure that *anyone*—from individual artists to large libraries—can feed clean, provenance-rich data into both EverArchive **and** the Internet Archive with minimal friction.

## 7  User Journeys  (How data flows into the system)

> These scenarios highlight *extension*, not replacement.  IA is an optional downstream mirror at multiple steps.

### 5.1  Creator Journey – "I want my work & process preserved"
1. **Onboard** via EverArchive portal (social / wallet / SSO).
2. **Deposit** source files, drafts, & explanatory notes.
3. **Select rights template** (e.g. CC-BY-SA, All-Rights-Reserved, or staged release).
4. **Seal provenance** – assets hashed, timestamped, and written to Chia Data Layer; DAO token minted granting creator governance rights.
5. **Mirror snapshot** – at release, a stable export pushes to IA (optional "Born-digital Collection: EverArchive/CreatorName").

**Tools touched:** *EverPortal* (primary UI), *EverCLI* (optional for batch creatives), *EverSDK* (CMS plug-ins), *EverNode* (background storage), *Chia Data Layer* (provenance).

### 5.2  Institutional Partner Journey – "University library wants to co-custody a collection"
1. Library joins DAO as a node operator.
2. Runs *EverNode* software → stores encrypted shards locally & publishes signed custody attestations.
3. Gains governance votes proportional to storage / funding commitments.
4. IA can harvest public portions as part of its institutional crawler, enriching its catalog.

**Tools touched:** *EverNode* (node software), *EverSDK* (integration), *EverScan* (custody dashboard), *DAO governance dApp*.

### 5.3  Researcher Journey – "Historian in 2040 needs full creative context"
1. Queries IA for public snapshot → retrieves canonical work.
2. IA metadata links to EverArchive DOI; researcher follows to view draft lineage, design files, discussion threads.
3. Access governed by rights policy (some assets open, some under controlled digital lending).

**Tools touched:** *EverScan* (lineage explorer), *EverPortal* (public view), *IA Wayback* (snapshot), *EverSDK* (API access).

### 5.4  Open-Source Contributor Journey – "Help improve preservation tooling"
* Fork EverNode client, submit PRs → rewarded by DAO.
* Brewster's advocacy could attract community devs who already contribute to IA.

**Tools touched:** *EverNode* (codebase), *EverCLI*, *EverSDK*, GitHub integrations.

> **Key takeaway for Brewster:** Every user flow either funnels *more complete metadata* into IA or leverages IA as a public distribution endpoint.  **No cannibalisation – only enrichment.**

### Creator Personas:
• **Indie Game Studio** – preserving Unity project files, design docs, and version history alongside the shipped game, creating a complete development archive for future game historians.
• **Physics Lab** – archiving raw spectrometer data, lab notebooks, and failed experiment logs alongside published papers, ensuring reproducibility and capturing the full scientific process.
• **Digital Poet** – storing iterative drafts of AI-collaborative poems with prompt histories and generation parameters, documenting the human-AI creative dialogue.
• **VR Artist** – preserving 3D scene files, shader code, and motion capture data for immersive experiences that would otherwise vanish when platforms shut down.
• **Cultural Institution** – digitizing indigenous art with full provenance chains, oral histories, and community permissions embedded in the preservation metadata.

## 8  Concrete Collaboration Ideas

| Idea | Benefit to IA | Benefit to EverArchive |
|------|---------------|------------------------|
| Provenance Push feed | Richer metadata & citation graph | Credibility + public discovery |
| Joint Creator Fellowship | Showcases new preservation paradigms | Validation & outreach |
| Shared CDL research | Advances licensing solutions | Aligns with DAO rights model |
| Co-branded fundraising | Leverage IA donor network | Seed EA endowment |

### Immediate Calls to Action for Brewster
1. **Name Harmony** – confirm "EverArchive" is distinct enough from "Internet Archive"; seek any guidance on potential confusion.
2. **Public Endorsement / Advisory Role** – explore having Brewster's name and IA's blessing on the project roadmap.
3. **Investor & Foundation Intros** – tap IA's supporter network (Mellon, NEH, IMLS, etc.) for preservation-aligned funding.
4. **Provenance Spec Co-authoring** – collaborate on an open schema (EverArchive + IA metadata) to standardise creator provenance across the web.
5. **Conference Showcase** – co-host a mini-session at the Distributed Creatives conference (Day 2) introducing the joint vision.
6. **Technical Integration** – detailed technical integration roadmap and specifications will be available soon at everarchive.org for review and collaboration.
7. **Funding & Endowment Guidance** – share lessons from IA's funding journey and advise on building EverArchive's preservation endowment **(target: $3–5 M seed fund to cover first 2 PB / 5 years)**.

### Open Collaboration & Ecosystem Awareness
* We recognise the wider preservation community (e.g., Starling Lab, FFDW, open-source archivists) is exploring similar provenance-first models; EverArchive is eager to plug into—rather than duplicate—those efforts.
* A nimble group of blockchain & decentralised-storage engineers (Chia core contributors, Filecoin node operators, etc.) has already committed part-time bandwidth; **funding is the remaining unlock**.
* Simple ways Brewster can help:
  1. Warm intros to IA staff or affiliated initiatives already experimenting with cryptographic provenance.
  2. Signal-boosting EverArchive in conversations with preservation funders (Mellon, NEH, IMLS, Filecoin Foundation).
  3. Advice on avoiding overlap with IA's in-house R&D; suggest co-development where missions align.
* Guiding principle: **"Many archives, one memory."** EverArchive aims to be *an* upstream context provider—IA remains the canonical public library.

## 9  Enthusiasm Hooks / Quotables

* "Brewster, you safeguarded billions of pages; let's safeguard the *minds* that built them."  
* "Together we can give future historians not just the page, but the *story* of how it came to be."  
* "Think of EverArchive as an upstream tributary feeding richer data into the great Library you've already built."  

---

## Appendix A – Evidence Base & Further Reading

| Area | Key Documents / Sources |
|------|-------------------------|
| Internet Archive landscape | • Active Work/Round 1/Research/Research 2/Internet Archive Operations and Partnerships/…executive-summary-250-words.md  <br/>• …analytical-brief-internet-archive-operations-and-partnerships.md |
| Provable provenance & NFTs | • EverMint prototype demos (repo link)  <br/>• Chia Data Layer RFC #1  <br/>• Starling Lab Provenance Framework (2024) |
| Economic model | • Active Work/Round 1/Research/Research 2/Endowment Feasibility – $100M Endowment Analysis.md  <br/>• Whitepaper v3 Working / 02-research-queue.md |
| Creator needs research | • Operations/Research Coordination/Comprehensive Research Prompts for EverArchive's Critical Evidence Priorities.md  <br/>• Academic Preservation Requirements – Institution Typology, Workflows, and Unmet Needs (multiple memos) |
| Vision & philosophy | • Canonical Library/Tome I – 1.0 Essence and Vision.md  <br/>• SACRED-VISION-DOCUMENT-EverArchive.md  <br/>• The EverArchive Manifesto.md |

> Additional bibliography and supporting materials available on request. 

## Appendix B – Possible Questions & Brief Responses (Meeting Cheat Sheet)

| Likely Question | Concise Response |
|-----------------|------------------|
| "What have you actually built?" | Prototype EverPortal, CLI, Node & Mint; technical specs at everarchive.org. Open-spec DAO & Deep Authorship Object drafted. |
| "How is this different from IA?" | IA preserves published web; EA preserves provenance & creative process—adds context, no overlap. |
| "Storage costs?" | Hybrid model: off-chain cold storage + DAO endowment; costs drop ~50 % / 2 yrs; detailed analysis in Endowment memo. |
| "Do creators really want this?" | MySpace/GeoCities losses scarred creatives; academics already requesting verifiable provenance; strong creator interest. |
| "Legal or rights risks?" | Rights captured at ingest; controlled lending; consult Creative Commons and CDL frameworks. |

> A more extensive Q&A set is available upon request. 

## Environmental Note

EverArchive is designed with energy efficiency in mind, leveraging cold-storage strategies and proof-of-space networks to minimise carbon footprint. 