# Supplementary Diagrams

Below are optional visuals you can display during the call to clarify specific concepts.  Render in any Mermaid-enabled viewer.

---

## 1. Deep Authorship Object – Three-Layer Stack
```mermaid
classDiagram
class CoreLayer {
  +hash tree
  +signatures
}
class ProcessLayer {
  +draft versions
  +rights metadata
}
class SurfaceLayer {
  +public snapshot
  +IA feed
}
CoreLayer <|-- ProcessLayer
ProcessLayer <|-- SurfaceLayer
```

---

## 2. Storage Fan-Out Map
```mermaid
graph TD
  EverNode --> ChiaDL[Chia Data Layer]
  EverNode --> Filecoin
  EverNode --> InstNode[Institutional Node]
  EverNode --> ColdVault[Quartz/Tape Vault]
  EverNode --> Glacier[S3 Glacier Tier]
```

---

## 3. Funding & Governance Loop
```mermaid
sequenceDiagram
  actor Donor
  participant DAO
  participant Storage
  participant Impact
  Donor->>DAO: Endowment funds
  DAO->>Storage: Pay for preservation
  Storage-->>Impact: Provenance metrics
  Impact-->>Donor: Transparency report
```

---

## 4. User Journey Swim-Lane (Creator vs Library vs Researcher)
```mermaid
sequenceDiagram
  participant Creator
  participant EA[EverArchive]
  participant IA[Internet Archive]
  participant Researcher
  Creator->>EA: Upload drafts + rights
  EA->>IA: Snapshot feed
  IA-->>Researcher: Public artifact
  Researcher->>EA: Fetch provenance & drafts
```

---

## 5. Risk-Mitigation Matrix (simplified)
```mermaid
flowchart LR
  subgraph Threats
    A(Bit Rot)
    B(Legal Takedown)
    C(Format Obsolescence)
    D(Energy Cost)
  end
  subgraph Mitigations
    E(Multi-hash Audits)
    F(CDL Policies)
    G(Decade Re-seal)
    H(Proof-of-Space)
  end
  A -- mitigated by --> E
  B -- mitigated by --> F
  C -- mitigated by --> G
  D -- mitigated by --> H
``` 