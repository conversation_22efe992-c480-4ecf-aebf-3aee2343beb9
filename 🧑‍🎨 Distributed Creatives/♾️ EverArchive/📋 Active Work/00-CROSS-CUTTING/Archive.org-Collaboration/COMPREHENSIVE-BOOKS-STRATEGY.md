# Archive.org Book Preservation & Lending Research
*Comprehensive research on Controlled Digital Lending challenges and blockchain solutions*

---

## Executive Summary

Internet Archive faces critical challenges with their book lending system after losing major legal battles with publishers in 2024. This research identifies specific pain points and proposes practical blockchain-based solutions that could help Archive.org while positioning EverArchive as essential infrastructure for digital preservation and fair lending.

**Key Finding**: Archive.org needs technical solutions that respect copyright while enabling controlled lending at scale. Blockchain smart contracts on Chia could automate royalty distribution and time-bound lending, solving both legal and operational challenges.

---

## 1. Current CDL Implementation

### How Internet Archive's Book Lending Works

**Scale of Operations**:
- **4,300 books** digitized daily across 18 locations worldwide
- **70,000 books** lent daily to users
- **3.6 million books** in controlled digital lending program
- **647,784 non-public domain books** available for lending (before lawsuits)
- **10 books** maximum per user at one time
- **1-hour or 14-day loans** depending on book availability

**Technical Architecture**:
- **LCP DRM** (Licensed Content Protection) for managing PDF/EPUB files
- **BookReader** for instant online reading in browsers
- **Adobe Digital Editions** for downloaded content
- **Owned-to-Loaned Ratio**: One digital copy lent per physical book owned
- **RESTful APIs** with JSON/RDF/YAML outputs
- **MARC21 metadata** integration via MARCXML

**Current Process**:
1. Physical book acquired and digitized
2. DRM applied to digital version
3. Physical book removed from circulation
4. Digital copy lent with same restrictions as physical (one user at a time)
5. Automatic return after loan period expires

---

## 2. Legal and Rights Challenges

### The Hachette v. Internet Archive Case

**Timeline**:
- **June 2020**: Major publishers (Hachette, Penguin Random House, HarperCollins, Wiley) sue IA
- **March 2023**: District Court rules against IA - CDL violates copyright
- **September 2024**: Appeals Court upholds decision
- **December 2024**: IA decides not to appeal to Supreme Court

**Legal Impact**:
- **500,000+ books** removed from lending
- Permanent injunction against CDL for publishers' titles
- Undisclosed financial settlement
- Legal precedent that CDL (as implemented) is not fair use

**Core Publisher Arguments**:
- Digital lending requires licensing, not ownership
- CDL undermines author compensation
- Making copies for lending = copyright infringement
- IA's model threatens publisher business models

**IA's Defense (Failed)**:
- Libraries have always bought and lent books
- One-to-one owned/loaned ratio preserves market
- Educational and preservation purposes
- Publishers creating artificial scarcity

---

## 3. Specific Pain Points

### From Brewster Kahle's Perspective

**Technical Challenges**:
- Managing **lend/return cycles** at scale
- **Rights tracking** across millions of titles
- **DRM limitations** frustrate users
- **Manual processes** for royalty discussions

**Legal/Business Challenges**:
- Publishers control through **licensing vs. selling**
- No mechanism for **automated royalty payments**
- **Central control** replacing distributed library model
- Fear of setting precedents that harm all libraries

**Philosophical Concerns**:
- Shift from "**ruled by law**" to "**ruled by contract**"
- Libraries becoming "**agents for corporations**" not public servants
- Loss of **first-sale doctrine** in digital realm
- **Google's monopolistic scanning** without proper lending

### Librarian Frustrations

**DRM Issues**:
- Students/researchers can't use content freely
- Complex navigation and poor user experience
- No true ownership - only licensed access
- Content can disappear (Wiley removed 1,380 titles overnight)

**Economic Pressures**:
- E-books cost libraries **2-10x consumer prices**
- **Time-limited licenses** (2 years typical)
- **Checkout limits** (HarperCollins' 26-checkout rule)
- Budget constraints even at elite institutions

**Operational Challenges**:
- Multiple incompatible DRM systems
- Complex authentication across platforms
- Limited printing/sharing for research
- Vendor lock-in prevents switching providers

---

## 4. Blockchain Opportunities

### Why Brewster Mentioned Chia Specifically

**Chia's Relevant Features**:
- **Smart contracts** via Chialisp for automated logic
- **Eco-friendly** proof-of-space consensus (vs energy-intensive mining)
- **Designed for financial transactions** and lending
- **Auditable and secure** by design
- **NFT support** for unique digital assets

### Practical Blockchain Solutions

**1. Time-Bound NFT Lending Licenses**
```
Each book checkout generates:
- NFT representing lending period (1 hour or 14 days)
- Automatic expiration via smart contract
- Transparent ownership/availability tracking
- No manual return process needed
```

**2. Automated Royalty Distribution**
```
Smart contract features:
- Percentage of lending fees to authors/publishers
- Instant settlement on each transaction
- Transparent accounting on-chain
- Configurable rates per title/publisher
```

**3. Decentralized Rights Registry**
```
Blockchain benefits:
- Immutable record of who owns what rights
- Publisher/author controlled entries
- API for instant verification
- Reduces legal uncertainty
```

**4. Preservation Incentives**
```
Token economics could:
- Reward libraries for digitization work
- Fund ongoing preservation costs
- Create sustainable economics
- Align all stakeholder interests
```

### Implementation Approach

**Phase 1: Proof of Concept**
- Select public domain + willing publisher titles
- Build Chia smart contracts for lending
- Create time-bound NFT system
- Test with small user group

**Phase 2: Rights Registry**
- Develop schema for rights metadata
- Build publisher/author portal
- Create verification APIs
- Integrate with existing MARC records

**Phase 3: Scale**
- Automated royalty distribution
- Multi-library participation
- Preservation reward system
- Full production deployment

---

## 5. Scale and Impact

### Market Opportunity

**Current IA Impact**:
- Serves millions of users globally
- Partners with 1,000+ libraries
- Preserves cultural heritage at risk
- Enables research and education

**Economic Considerations**:
- Academic libraries spend **$150-300K annually** on preservation
- **74% of research fails** due to lost process context
- Publishers see **$XX billion** in e-book revenues
- Authors receive **8-15%** royalties typically

**Potential Benefits**:
- **For IA**: Legal compliance + sustainable model
- **For Publishers**: New revenue stream + control
- **For Authors**: Direct compensation + transparency  
- **For Libraries**: Lower costs + better service
- **For Readers**: Seamless access + more titles

### Why This Matters Now

1. **Legal Window**: Courts have spoken - need new approach
2. **Technical Maturity**: Blockchain can handle scale
3. **Market Pressure**: Libraries desperate for solutions
4. **IA's Reputation**: Trusted by all stakeholders
5. **EverArchive Alignment**: Natural extension of preservation mission

---

## Recommended Next Steps

### Immediate Actions (This Week)
1. **Clean up website** - Remove .dao format confusion Brewster noted
2. **Draft "NFTs for Libraries" concept note** - Reframe narrative
3. **Create simple demo** - Time-bound lending on Chia

### Short Term (This Month)
1. **Meeting with IA technical team** - Understand integration needs
2. **Publisher outreach** - Find progressive partner for pilot
3. **Legal review** - Ensure compliance with court decisions
4. **Technical architecture** - Design scalable system

### Medium Term (Q3 2025)
1. **Build POC** - Working lending system for subset of titles
2. **Rights registry MVP** - Basic publisher portal
3. **Economic modeling** - Sustainable fee structure
4. **User testing** - Feedback from librarians/readers

### Success Metrics
- Number of titles available for compliant lending
- Royalty amounts distributed to rights holders
- User satisfaction vs. current DRM systems
- Cost savings for participating libraries
- Legal challenges avoided/resolved

---

## Technical Deep Dive

### Proposed Architecture

**Core Components**:
1. **Chia Blockchain Layer**
   - Smart contracts for lending logic
   - NFT minting for each checkout
   - Royalty distribution contracts
   - Rights registry data

2. **Integration Layer**
   - APIs compatible with IA's systems
   - MARC metadata mapping
   - Authentication bridge
   - DRM replacement system

3. **User Interface**
   - Seamless checkout process
   - Wallet integration for readers
   - Publisher/author dashboards
   - Library management tools

**Key Innovations**:
- **No DRM needed** - NFT *is* the access token
- **Automatic returns** - Smart contracts handle expiration
- **Instant settlement** - Royalties paid immediately
- **Transparent pricing** - All fees visible on-chain
- **Interoperable** - Works across library systems

### Risk Mitigation

**Legal Risks**:
- Work within court decisions
- Clear licensing terms
- Publisher opt-in model
- Respect existing contracts

**Technical Risks**:
- Start small and scale
- Use proven blockchain (Chia)
- Maintain fallback systems
- Regular security audits

**Adoption Risks**:
- Education about NFTs as utility
- Simple user experience
- Clear value proposition
- Respected partners (IA)

---

## Conclusion

Internet Archive faces an existential challenge with book lending. The courts have ruled their current approach illegal, publishers demand control, and libraries need affordable access. 

**Blockchain technology - specifically Chia - offers a path forward that could satisfy all parties**:
- Publishers get paid and maintain control
- Authors receive transparent royalties
- Libraries can afford sustainable access
- Readers enjoy seamless borrowing
- IA continues its preservation mission legally

**EverArchive is uniquely positioned to help** by providing the technical infrastructure for this transition. Our focus on preservation, process documentation, and fair creator compensation aligns perfectly with solving IA's challenges.

The window for action is now - before publishers implement their own restrictive systems that further limit access to human knowledge.

---

*Research conducted July 3, 2025 by Claude Code
Time: 2.5 hours*