# CONFERENCE DELIVERABLES - From Yesterday's Meeting

## 🎯 CORE DELIVERABLES NEEDED

Based on the recent <PERSON> call and meeting notes, these are the ACTUAL deliverables needed:

### 1. Book Archive System Proposal
- **Chia-based royalties & licensing smart-coins** concept
- How EverArchive can support IA's book-checkout model
- Time-bound NFT licenses for digital lending
- Royalty routing to authors via offers

### 2. NFTs Reframing Document
- Position NFTs as **data preservation primitives** (not speculative assets)
- "NFTs for Libraries" concept note
- Outline Chia NFT1 + schema.org bundles for digital books
- Show how NFTs store real cultural data

### 3. Website Cleanup (URGENT)
- Fix .dao format confusion (reads like blockchain DAO)
- Clear explanation that it's "Deep Authorship Object" not blockchain
- Update any confusing terminology

### 4. Prototype/Demo Materials
- Royalty smart-coin demo tied to public-domain text
- Show practical implementation of concepts

### 5. Partnership Integration Points
- How EverArchive complements (not competes with) IA
- Specific collaboration opportunities
- Clear value proposition for IA

## 📋 YESTERDAY'S ACTION ITEMS

From Brewster Call Notes:
- [ ] Draft "NFTs for Libraries" concept note
- [ ] Create royalty smart-coin demo/prototype
- [ ] Collect IA digital lending volume stats
- [ ] Clean up website ASAP (especially .dao confusion)

## 🔄 INTEGRATION WITH EXISTING MATERIALS

We have good foundation materials, but need to:
1. **Pivot** from general conference prep to book/library focus
2. **Emphasize** practical blockchain use for libraries
3. **Demonstrate** real implementation, not theory
4. **Clarify** technical terms that confuse

## ⚡ IMMEDIATE PRIORITIES

1. **Website .dao fix** - This is causing confusion NOW
2. **NFTs for Libraries** one-pager - Core concept
3. **Simple demo** - Show it working
4. **Partnership proposal** - Concrete next steps

---

This is what Brewster actually cares about based on yesterday's conversation!