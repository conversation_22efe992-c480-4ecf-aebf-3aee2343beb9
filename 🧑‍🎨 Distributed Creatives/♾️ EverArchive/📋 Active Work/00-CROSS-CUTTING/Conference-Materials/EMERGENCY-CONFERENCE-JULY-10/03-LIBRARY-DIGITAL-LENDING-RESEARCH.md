# Library Digital Lending Models Research
**Date**: July 3, 2025
**Research Question**: Current library digital lending models - technical architecture, rights management, and implementation approaches
**Confidence Level**: High

## Executive Summary
Library digital lending operates through three primary models: Controlled Digital Lending (CDL), commercial platforms (OverDrive/Libby, Hoopla), and open-source alternatives (SimplyE/Palace Project). CDL faces significant legal challenges following the 2024 court ruling against Internet Archive, while commercial platforms dominate with restrictive licensing models that cost libraries 3-5x retail prices. Open-source alternatives are emerging but face sustainability challenges.

## Key Findings

### 1. Controlled Digital Lending (CDL)
**Discovery**: CDL implements a strict "owned-to-loaned" ratio where libraries can only lend the number of digital copies equal to physical copies owned.

**Technical Implementation**:
- **Core Process**: Physical book → Digitization → DRM application → Physical copy sequestered
- **DRM Requirements**: Prevents copying, time-limits access, single-user checkout
- **Technical Controls**: Adobe Content Server commonly used for DRM
- **Authentication**: Integration with library ILS for patron verification

**Evidence**: 
- NISO developing consensus framework for CDL standards
- Multiple implementations using Google Drive + Apps Scripts for simple systems
- Ex Libris making library software CDL-compatible

**Legal Status (2024)**:
- September 2024: Second Circuit upheld ruling against Internet Archive
- December 2024: Internet Archive declined Supreme Court appeal
- Future uncertain but libraries continue implementing within legal constraints

**Confidence**: High (court documents and technical specifications widely documented)

### 2. OverDrive/Libby
**Discovery**: Dominant commercial platform (90%+ market share) using publisher-controlled licensing with significant cost premiums for libraries.

**Technical Architecture**:
- **API Structure**: RESTful APIs with JSON responses
- **DRM**: Adobe Content Server (ACSM files for EPUB/PDF)
- **Authentication**: 8-digit setup codes, seamless ILS integration
- **Cross-device Sync**: Reading progress synchronized across devices
- **Content Delivery**: One copy/one user model enforced by DRM

**Licensing Model**:
- **Cost Structure**: $55 for 2-year license vs $15 consumer perpetual
- **Restrictions**: 26 loans or 2 years, whichever comes first
- **Physical Comparison**: Digital ~3x more expensive than hardcover
- **Usage Growth**: 739 million checkouts in 2024 (17% increase)

**Integration Features**:
- Library Account API for collection management
- Metadata API with Libby share links (June 2024 update)
- ILS integration for patron authentication
- Support for multiple content providers

**Confidence**: High (developer documentation and library reports)

### 3. SimplyE (Retiring 2024-2025)
**Discovery**: NYPL's open-source solution being sunset June 2024, with libraries transitioning to Palace Project.

**Technical Stack**:
- **Circulation Manager**: Handles authentication, content aggregation
- **Metadata Wrangler**: Merges information sources for discovery
- **OA Content Server**: Serves open access content
- **OPDS Web Catalog**: Standards-based catalog format
- **Client Apps**: iOS/Android using Readium SDK

**Architecture Benefits**:
- Aggregates multiple vendors (OverDrive, CloudLibrary, Axis 360)
- Open source and standards-based (OPDS, EPUB3)
- Unified interface across content sources
- No vendor lock-in

**Retirement Reasons**:
- Sustainability challenges
- Limited adoption beyond NYPL
- Resource-intensive maintenance

**Confidence**: High (official documentation and sunset announcements)

### 4. Other Relevant Systems

**Palace Project** (SimplyE Successor):
- LYRASIS partnership with DPLA
- Using SimplyE open-source components
- Commercial support model for sustainability
- Knight Foundation and IMLS funding

**Hoopla**:
- Pay-per-use model (no waiting lists)
- Costs rising 450% (from $2.40 to $11 per checkout)
- Libraries dropping due to unsustainable costs
- Standard collection across all libraries

**CloudLibrary**:
- Acquired by OCLC for $25.5M (2024)
- 500 library subscribers
- 80% compliance score
- Moving to integrate with OCLC infrastructure

**Confidence**: High (industry reports and announcements)

## Comparative Analysis

| Criteria | CDL | OverDrive/Libby | SimplyE/Palace | Hoopla |
|----------|-----|-----------------|----------------|---------|
| **Cost Model** | Infrastructure only | Per-title licensing | Open source/Support | Pay-per-use |
| **DRM Approach** | Adobe/Custom | Adobe Content Server | Flexible/Adobe | Streaming DRM |
| **Ownership** | Library owns | Publisher controls | Library controls | No ownership |
| **Wait Lists** | Yes (1:1 ratio) | Yes (licensing limits) | Yes (varies) | No (instant) |
| **Technical Complexity** | High | Medium (turnkey) | High | Low |
| **Vendor Lock-in** | None | High | None | Medium |
| **Legal Risk** | High (post-ruling) | None | None | None |
| **Sustainability** | Uncertain | Commercial | Transitioning | Cost challenges |

## Technical Implementation Patterns

### Common Architecture Elements
1. **Authentication**: ILS integration via SIP2/API
2. **DRM**: Adobe Content Server dominant
3. **Lending Mechanics**: Time-based licenses with auto-return
4. **Content Delivery**: EPUB/PDF with DRM wrapper
5. **Discovery**: OPDS catalogs or proprietary APIs

### Rights Management Approaches
- **CDL**: Strict 1:1 physical-to-digital ratio
- **Commercial**: Publisher-defined terms (time/loan limits)
- **Open Access**: No DRM for public domain/OA content
- **Streaming**: Online-only access (Hoopla model)

### Integration Requirements
- Library ILS compatibility
- Patron authentication systems
- Analytics and reporting
- Collection development tools
- Discovery layer integration

## Recommendations for Decentralized Alternative

### 1. Technical Architecture
- Adopt OPDS standards for interoperability
- Implement flexible DRM options (including DRM-free)
- Design for federated authentication
- Build on Palace Project learnings

### 2. Rights Management
- Support multiple lending models (CDL, licensed, OA)
- Implement cryptographic proof of ownership
- Enable library-controlled terms
- Design for legal compliance flexibility

### 3. Sustainability Model
- Consider hybrid open-source with paid support
- Enable library consortia ownership
- Implement usage-based cost sharing
- Design for long-term maintenance

### 4. Key Differentiators
- True ownership for libraries
- Transparent cost structures
- Privacy-preserving architecture
- Community governance model

## Knowledge Gaps
- Post-2024 CDL implementation strategies
- Detailed Palace Project technical specifications
- International lending model variations
- Web3/blockchain experiments in library lending
- Patron privacy preservation techniques

## Sources
**Primary Sources**:
- NISO IS-CDL Working Group specifications
- OverDrive Developer Portal documentation
- Library Simplified/SimplyE GitHub repositories
- Court documents: Hachette v. Internet Archive (2024)

**Secondary Sources**:
- Library Technology Guides analysis (2024-2025)
- American Libraries Magazine systems report
- Digital Banking Report on lending models
- Library community discussions and implementations

## Next Steps
1. Deep dive into Palace Project architecture
2. Investigate international CDL implementations
3. Research blockchain/DLT experiments in libraries
4. Analyze patron privacy requirements
5. Study consortia governance models