# Agent Briefing: EverArchive Reorganization & Launch Preparation

## Quick Start

You are taking over the EverArchive project - a 135-file Obsidian vault that needs reorganization for:
- **Landing page launch**: Target July 14, 2025
- **Conference panel**: With <PERSON> on July 28, 2025

## Essential Files to Read First (In Order)

### 1. Handover Documentation (Created by Previous Agent)
**Location**: `/z - launch strategy/`
- **`START-HERE-next-agent.md`** - Your 5-minute orientation guide
- **`handover-for-next-agent.md`** - Comprehensive situation analysis
- **`reorganization-options-analysis.md`** - Four strategic approaches compared
- **`conflicting-approaches-guide.md`** - Key philosophical tensions explained

### 2. Project Core Documents
- **`/README.md`** - Official project overview (note: references structure that doesn't exist)
- **`/CLAUDE.md`** - Technical instructions for working with this codebase
- **`/z - launch strategy/plan.md`** - Sophisticated strategy doc (may be aspirational)
- **`/z - launch strategy/launch strategy.md`** - Phased rollout plan for website

### 3. Key Content Files (When You Need Them)
- **`/📚 Canonical Library/Weighted Insights & Top-Priority Signals.md`** - User research insights
- **Multiple versions of "Deep Authorship Manifesto"** scattered throughout
- **Whitepaper** - Only exists as "Draft 1" in `docs/whitepaper/`

## Critical Context from Previous Analysis

### The Core Problem
The EverArchive brilliantly documents a vision for "civilizational memory infrastructure" based on Deep Authorship principles (preserving creative process, not just products). However, the project itself suffers from:
- **3+ parallel organizational structures** (Canonical Library, Centering the Archive, _archive)
- **5+ versions** of key documents with no clear authority
- **Missing infrastructure** (CHANGELOG/, scripts/, proper docs/ folders)
- **Philosophical tension** between preserving messy process and having clean, findable docs

### What We Found

**Total Content**: 135 markdown files, 5.1MB, across 34 directories

**Key Structures**:
1. **📚 Canonical Library/** - 17 files organized into 4 Tomes (seems most authoritative)
2. **Centering the Archive/** - Alternative organization with "EA-" prefixed files
3. **📦 _archive/** - Mix of old versions AND current content
4. **Research/** - Duplicated across multiple rounds

**Critical Gaps**:
- Empty file: `xx-temp/List of Delivered Documents & Artifacts.md`
- Unnamed 27KB file in Canonical Library
- No final whitepaper (only Draft 1)
- Missing directories referenced in README

## Your Strategic Options (From Our Analysis)

### Option 1: "Canonical Library First" (1-2 days, Low Risk)
Make `📚 Canonical Library/` the single source of truth, archive everything else

### Option 2: "L1/L2/L3 Distillation" (3-5 days, Medium Risk)
Follow plan.md's three-layer model:
- L1 = Canonical Library (technical/builders)
- L2 = Strategic documents (partners/funders)
- L3 = Public content (general audience)

### Option 3: "Chunk-Based Workflow" (1 week+, High Risk)
Implement plan.md's sophisticated chunk system with work folders and outputs

### Option 4: "Minimal Intervention" (4-8 hours, Very Low Risk)
Just fix critical gaps, create presentation package, defer major reorg

## Critical Questions You MUST Ask First

Before doing anything:

1. **"Is the strategy in plan.md (with L1/L2/L3 layers and chunks) your official approach, or should I consider simpler alternatives?"**

2. **"Do the July 14 website launch and July 28 conference dates still apply?"**

3. **"For the immediate launch, would you prefer I focus on creating a clean presentation package, or attempt the full reorganization?"**

4. **"Should I treat the existing Canonical Library structure as authoritative, or is everything open to reorganization?"**

5. **"The launch strategy.md describes a phased website rollout starting with a scaffold - should I prepare content for this approach?"**

## Recommended Approach (Pending User Answers)

### If Time is Critical (July deadlines active):
1. Start with Option 4 (minimal intervention)
2. Create `/launch-critical/` folder with:
   - 5 essential documents for presentation
   - Quick whitepaper completion
   - Simple navigation guide
3. Prepare content for phased website per launch strategy.md

### If Deep Reorganization Preferred:
1. Implement Option 2 (L1/L2/L3 model)
2. This aligns with existing plan.md
3. Preserves process while improving findability

## The Meta-Insight

This project about preserving creative process has a messy creative process. Consider proposing that this reorganization itself become the first Deep Authorship case study - documenting how order emerges from creative chaos.

## Your First Actions

1. **Read the START-HERE-next-agent.md file**
2. **Ask the 5 critical questions above**
3. **Based on answers, choose from the 4 strategic options**
4. **Create implementation checklist**
5. **Start with highest-impact, lowest-risk tasks**

## Success Metrics

You'll know you've succeeded when:
- User has clear, accessible content for website launch
- Conference presentation materials are ready
- Original vision remains intact
- Process artifacts are preserved (even if reorganized)
- Future contributors can navigate the structure

## Additional Notes

- The project uses Obsidian as its knowledge management system
- Git repository with recent commits showing active development
- Multiple AI assistants have contributed to documentation
- Deep philosophical commitment to preserving complexity and process
- Timeline pressure may require pragmatic compromises

---

**Start your response with**: "I've reviewed the comprehensive handover documentation and understand the EverArchive project needs reorganization for a July 14 website launch and July 28 conference presentation. Before proceeding, I need clarity on a few critical decisions..."

Good luck! Remember: The perfect is the enemy of the good, especially with launch deadlines.