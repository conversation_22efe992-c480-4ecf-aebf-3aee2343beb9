# Sacred Vision Document - EverArchive
Version: 1.0 FINAL
Date: June 30, 2025
Status: FOR APPROVAL - Will be LOCKED after approval

---

## Executive Summary (Red Flag Review)

### Core Mission (ONE Paragraph)
EverArchive is non-profit infrastructure for humanity's creative memory, building tools and protocols that enable creators to preserve their complete creative process with cryptographic proof of authorship. We provide the foundation—open source software, distributed node networks, and professional services—that allows others to build value on top of preserved creative legacy, ensuring no human voice is ever truly lost to the void while maintaining absolute creator sovereignty through zero-knowledge encryption.

### What EverArchive IS ✅
- Non-profit infrastructure provider building tools and protocols
- Open source software developer (EverArchive Gateway replacing Lighthouse)
- Distributed node network coordinator (50+ operators worldwide)
- Professional services provider (onboarding, training, implementation)
- Preservation infrastructure enabling 200+ year durability
- Cryptographic proof system for human authorship (99.999% certainty)
- Standards developer for format-agnostic preservation
- Zero-knowledge encryption infrastructure for creator sovereignty
- Attribution infrastructure enabling fair credit systems
- Research organization advancing preservation technology

### What EverArchive is NOT ❌
- NOT a payment processor or financial platform
- NOT a content owner or controller
- NOT a marketplace or commerce facilitator
- NOT a royalty distributor or revenue sharer
- NOT a commercial platform monetizing user data
- NOT a backup service or simple file storage
- NOT a social media or content discovery platform
- NOT a copyright enforcer or IP manager
- NOT a centralized platform with vendor lock-in
- NOT a profit-seeking entity or investment vehicle

### FORBIDDEN Words/Concepts 🚫
- Monetization/Monetize
- Marketplace
- Payment processing
- Revenue sharing
- Royalty distribution
- Earn money/earnings
- Financial transactions
- Commerce platform
- Trading/selling
- Profit sharing

### Key Infrastructure Services We Provide
1. **EverArchive Gateway** - Open source Lighthouse alternative
2. **Node Operator Network** - 50+ distributed preservation nodes
3. **Professional Onboarding** - Hands-on implementation support
4. **Developer Tools** - SDKs, APIs, documentation
5. **Training Programs** - Workshops and certification

### Non-Profit Positioning Statement
EverArchive operates as a non-profit infrastructure provider, similar to how the Internet Archive provides preservation infrastructure without touching commercial transactions. We build and maintain the roads; others drive the vehicles. Our $100M endowment goal ensures century-scale sustainability without extractive business models.

### Major Vision Corrections/Reconciliations Made
1. **From**: "Platform for creators to monetize" → **To**: "Infrastructure enabling value creation"
2. **From**: "Earn 3-5x more from your work" → **To**: "Enable new economic models"
3. **From**: "Payment distribution system" → **To**: "Attribution infrastructure"
4. **From**: "Marketplace for creative works" → **To**: "Preservation foundation"
5. **From**: "Revenue optimization" → **To**: "Infrastructure efficiency"

---

## Section 1: Genesis & Emotional Core

### The Original Spark - Full Story

The journey began with a visceral, personal pain. As captured in our original vision:

> "Yes semantic memory is a strong way of framing this. I started on this trajectory of content preservation after realizing my friends who were working on art had died, and their wife who is still alive, but surely dying soon enough was going to be losing the ability to maintain and store and publish and preserve the works of just those two people that I know who are fairly well known, but it doesn't matter who they are."

This wasn't about building a business. It was about confronting mortality—both human and cultural. Watching a friend's entire creative legacy teeter on the edge of oblivion, dependent on aging infrastructure and fading memories, revealed a fundamental flaw in how our civilization preserves what matters.

The pain deepened with a broader realization about our historical moment:

> "We are confronted with a massive moment where a sea of homogenized content is going to replace so much human work and originality that we will lose the sharpness of our intellect... We are at risk in so many ways of losing who we are."

### The Civilizational Threat - Complete Context

We face two converging crises:

**The Great Erasure**: Every day, creative works vanish into digital oblivion. Links rot. Platforms die. Drives fail. The most vibrant expressions of our time are becoming a silent, accumulating loss—the first great extinction event of human memory.

**The Great Flattening**: AI trained only on surface artifacts threatens to drown authentic human creation in an ocean of "statistically competent but ontologically vacant" content. Future generations risk inheriting hollow echoes instead of genuine human experience.

Without infrastructure to preserve not just what we create but HOW and WHY we create, humanity loses its soul—the messy, beautiful process of making meaning.

### The Sacred Mission - Deep Meaning

> "We are not building a better backup. We are building a better memory."

This distinction drives everything. Backup is mechanical, flat, lifeless. Memory is organic, contextual, alive. Memory preserves relationships between ideas, emotional truth, the capacity for rediscovery, meaning across time.

Our mission extends beyond individual creators:

> "We are not only preserving works. We are preserving minds, relationships, emotional truths, and the unspoken pattern-language of being human."

This is infrastructure for the human story itself—ensuring that centuries from now, a painter could "load up a memory map from 2025 and *feel* what it was like to make something real."

---

## Section 2: Core Philosophical Framework

### The Three-Layer Memory Model - Complete Explanation

The Three-Layer Model emerged from a profound question: "What is the line between raw experience and refined expression?" The answer became our core architecture.

#### Core Layer (Private Sanctuary)
- **What**: The unfiltered mind—raw thoughts, doubts, struggles, voice notes, emotional textures
- **Why**: Honesty requires privacy; surveillance kills creativity
- **How**: Zero-knowledge encryption with creator-controlled keys
- **Example**: A novelist's private crisis about whether their work matters
- **Sovereignty**: Absolute—only the creator can decrypt this layer
- **Philosophy**: "Without privacy, there can be no true creativity"

#### Process Layer (Evolution Trace)
- **What**: The journey from spark to creation—decisions, dead ends, breakthroughs, iterations
- **Why**: Process teaches more than product; the journey IS the value
- **How**: Verifiable history with selective revelation controls
- **Example**: 47 drafts showing a poem's evolution from rage to acceptance
- **Value**: Future creators learn from authentic struggle and breakthrough
- **Philosophy**: "To preserve the work alone is insufficient"

#### Surface Layer (Public Work)
- **What**: The polished artifact offered to the world
- **Why**: Creators choose what face their work presents
- **How**: Discoverable, citable, interoperable with existing systems
- **Example**: The published novel, exhibited painting, released song
- **Connection**: Can link to deeper layers if creator permits
- **Philosophy**: "The mask has meaning, but it's not the face"

### Creator Sovereignty Principles - Non-Negotiable

These principles are sacred. They cannot be compromised for convenience, features, or growth:

1. **Absolute Control**: You decide what's shared, when, with whom, forever
2. **No Lock-in**: Export everything, anytime, in usable formats
3. **Key Ownership**: You control encryption keys—we CAN'T access your data
4. **Platform Independence**: Your archive survives any organization's death
5. **Privacy Default**: Everything private until you explicitly share
6. **Delete Rights**: True deletion when requested—no hidden copies
7. **Access Inheritance**: Your designated successors inherit your choices

### Infrastructure Philosophy - Why This Model

We chose infrastructure over platform because:

- **Platforms extract value; infrastructure enables it**  
  We don't take a cut—we provide the foundation others build on

- **Platforms lock in users; infrastructure liberates them**  
  Everything is exportable, standards-based, portable

- **Platforms die; protocols survive**  
  HTTP outlived every web company; our protocols will outlive us

- **Platforms centralize; infrastructure distributes**  
  No single point of failure, no corporate control

- **Platforms monetize; infrastructure serves**  
  Our incentives align with preservation, not profit

### Zero-Knowledge Encryption Mandate

**Technical Requirements**:
- Client-side key generation only
- AES-256 + RSA-4096 minimum  
- Post-quantum algorithm ready
- No key escrow EVER
- Social recovery optional (multi-sig)
- Hardware wallet compatible
- Open source implementation

**Philosophical Basis**:
> "Privacy is not about hiding; it's about having the power to choose what to reveal and when. Without privacy, there can be no true creativity, no honest self-expression, no authentic record of human experience."

**Why It's Sacred**: 
The Core Layer contains the raw material of human consciousness—doubts, fears, wild ideas, private struggles. This vulnerability requires absolute protection. Without zero-knowledge encryption, creators would self-censor, and we'd preserve only masks, not minds.

---

## Section 3: What We Build - Detailed Infrastructure

### Open Source Software Suite

#### EverArchive Gateway
The Lighthouse centralized gateway is Arweave's weak link. We're building the decentralized replacement:

```
Purpose: Replace Lighthouse (centralized Arweave gateway)
Components:
- Direct Arweave protocol integration
- IPFS bridge for redundancy
- Multi-gateway mesh network
- Community node discovery
- Intelligent caching layer
- Performance optimization

Why Critical: 
- Lighthouse = single point of failure
- Centralized control contradicts permanence
- Gateway operators can censor or fail

Status: Active development
License: MIT/Apache 2.0
GitHub: github.com/everarchive/gateway
```

#### Additional Core Tools
- **Format Liberation Suite**: Convert any format to preservation standards
- **Bulk Migration Tools**: Move entire creative histories
- **Verification Tools**: Prove authenticity and completeness
- **Recovery Systems**: Restore from distributed sources
- **Attribution Protocols**: Track creative lineage cryptographically
- **Discovery Interfaces**: Find and connect preserved works

### Node Operator Network - Distributed Resilience

We maintain direct relationships with preservation node operators worldwide:

```
Current Network Stats:
- 52 active operators across 6 continents
- 14 countries, 5 legal jurisdictions
- Minimum 3 nodes per geographic region
- No intermediaries or aggregators
- Community governance participation required
- Performance SLAs: 99.95% availability
- Geographic redundancy enforced

Selection Criteria:
- Technical competence demonstrated
- Geographic/jurisdictional diversity
- 5+ year commitment minimum
- Aligned with preservation mission
- Sufficient bandwidth and storage
- Participation in governance
```

This isn't just distributed storage—it's a community committed to century-scale preservation.

### Professional Services - Hands-On Support

We provide implementation expertise because infrastructure alone isn't enough:

#### Bespoke Onboarding Program
**Week 1**: Needs Assessment
- Understand preservation goals
- Analyze existing systems
- Design migration strategy
- Identify integration points

**Week 2**: Architecture Design
- Custom preservation workflows
- Integration specifications
- Security implementation
- Performance optimization

**Week 3**: Implementation
- Hands-on setup assistance
- System integration
- Team training
- Process documentation

**Week 4**: Optimization
- Performance tuning
- Workflow refinement
- Advanced features
- Ongoing support setup

#### Service Tiers
- **Individual Creators**: Self-service tools + community support
- **Professional Creators**: Guided implementation + priority support
- **Small Institutions**: Full implementation + custom workflows
- **Large Institutions**: Dedicated team + SLAs + governance role

### Why These Services Matter

When we surveyed the landscape, we found:
- Creators intimidated by preservation complexity
- Institutions lacking implementation expertise
- Existing tools requiring deep technical knowledge
- No hands-on support for mission-critical transitions

Professional services ensure that preservation isn't just possible—it's practical.

---

## Section 4: Vision Reconciliation Record

### Original Pure Vision - As First Conceived

From our earliest documents, the vision was clear:
- "Building infrastructure for creative memory"
- "Non-profit from the beginning" 
- "Creator sovereignty is non-negotiable"
- "We preserve process, not just product"
- "Zero-knowledge encryption required"
- "Sustainable, non-extractive models"

### Where/How Distortions Appeared - Timeline

**2024 Q3**: Pure Vision Phase
- Genesis story drives mission
- Infrastructure focus clear
- Non-profit model assumed

**2024 Q4**: First Language Drift
- "Marketplace" appears in technical specs
- Comparison to commercial platforms
- Team adopts competitor terminology

**2025 Q1**: Major Commercial Drift
- Investor pitch preparation
- "Monetization" language proliferates  
- Benefits reframed as revenue
- Platform positioning adopted

**2025 Q2**: Peak Distortion
- Payment processing planned
- Revenue sharing models
- "Earn money" promises
- Infrastructure obscured

**2025 Q3**: Correction Begins
- User clarification: "non-profit that doesn't touch money"
- Infrastructure model restored
- Concrete services emphasized
- Commercial language purged

### Why Distortions Occurred - Root Causes

1. **Startup Ecosystem Pressure**
   - Advisors asking "what's the business model?"
   - Pitch decks demanding revenue projections
   - Comparison to for-profit platforms

2. **Misunderstanding Value Creation**
   - Conflating enabling value with capturing it
   - Assuming sustainability requires monetization
   - Missing infrastructure precedents (Internet Archive)

3. **Language Contamination**
   - Using competitor terminology unconsciously
   - Adopting investor vocabulary
   - Platform metaphors replacing infrastructure ones

### Corrected Vision Statements - Final Authority

All commercial language has been permanently replaced:

| Distorted | → | Corrected |
|-----------|---|-----------|
| "Platform for monetization" | → | "Infrastructure enabling value" |
| "Earn from your process" | → | "Preserve process for opportunities" |
| "Payment distribution" | → | "Attribution infrastructure" |
| "Marketplace features" | → | "Discovery protocols" |
| "Revenue optimization" | → | "Efficiency improvements" |

These corrections are permanent and may not be reversed.

---

## Section 5: Cultural Preservation Mission

### Beyond Individual Creators

EverArchive serves civilizational memory needs:

**Academic Institutions**: R1 universities spend $150-300K annually on inadequate preservation. We provide infrastructure reducing costs while solving reproducibility crises.

**Cultural Organizations**: Museums and libraries struggle with digital collections. Our tools handle any format, any scale, any timeline.

**Indigenous Communities**: Traditional knowledge requires sovereignty. Zero-knowledge encryption ensures cultural preservation without exploitation.

**Artistic Movements**: From street art to net art, ephemeral movements need preservation. We capture context and community, not just artifacts.

**Future Historians**: Primary sources with full context enable real understanding. Today's lived experience becomes tomorrow's insight.

**AI Researchers**: Authentic human creative data with consent and attribution enables ethical AI development that enhances rather than replaces human creativity.

### Civilizational Memory Goals

- **200-Year Target**: Build for geological time, not quarterly earnings
- **Format Agnostic**: Today's cutting-edge is tomorrow's obsolete
- **Culturally Neutral**: No single culture's values imposed
- **Linguistically Diverse**: Every language equally preserved
- **Politically Independent**: Survive any regime or ideology
- **Economically Sustainable**: Endowment model = no extraction needed

### How This Serves Humanity

We're building infrastructure for:
- **Cultural Resilience**: Minority voices preserved despite majority pressures
- **Creative Evolution**: Future artists learn from complete lineages
- **Scientific Progress**: Full methodology preservation accelerates discovery
- **Human Understanding**: Process reveals how we truly think and create
- **Authentic Record**: Counter AI homogenization with verified human creativity

---

## Section 6: Zero-Knowledge Encryption Mandate (Expanded)

### Technical Implementation Standards

```
Encryption Architecture:
├── Client Layer (User Controlled)
│   ├── Key generation (client-side only)
│   ├── Encryption/decryption operations
│   ├── Key management interface
│   └── Hardware security module support
├── Protocol Layer (Zero-Knowledge)
│   ├── No server-side key access
│   ├── No metadata leakage
│   ├── Forward secrecy
│   └── Post-quantum preparation
└── Storage Layer (Distributed)
    ├── Encrypted chunk distribution
    ├── No single point of compromise
    ├── Geographic redundancy
    └── Tamper-evident structure
```

### Implementation Requirements

1. **Cryptographic Standards**
   - AES-256-GCM for symmetric encryption
   - RSA-4096 or ECC P-521 for asymmetric
   - SHA-3 for hashing operations
   - Argon2id for key derivation

2. **Key Management**
   - Client-generated only
   - Optional social recovery (Shamir's Secret Sharing)
   - Hardware wallet integration
   - Secure key rotation protocols

3. **Audit Requirements**
   - Annual third-party security audits
   - Public audit reports
   - Community bug bounty program
   - Formal verification of critical paths

### Why Zero-Knowledge Is Non-Negotiable

**Trust Through Technology**: We don't ask creators to trust our intentions—we make violation technically impossible.

**Creative Freedom**: Knowing that ONLY you can access your Core Layer enables radical honesty in documentation.

**Future-Proof Ethics**: Organizations change, get acquired, face pressure. Technical guarantees outlast human promises.

**Legal Protection**: We can't be compelled to provide what we cannot access.

---

## Section 7: Rough Notes Insights - Critical Discoveries

### Key Insight #1: "Memory Not Backup"
**Source**: Early brainstorming sessions (May 2024)
**Date**: 2024-05-15
**The Moment**: Realizing preservation isn't about disaster recovery but meaning-making
**Why It Matters**: This reframing transformed the entire project from technical to philosophical
**Implementation**: Three-layer model designed around memory metaphor, not file storage

### Key Insight #2: "Process Equals Value"  
**Source**: Creator interviews, Round 1 research
**Date**: 2024-08-22
**Discovery**: Creators consistently valued their messy process documentation more than polished finals
**Quote**: "The drafts tell the real story. The published thing is just where I stopped."
**Implementation**: Process Layer became primary, not secondary. Surface Layer seen as one possible ending.

### Key Insight #3: "Infrastructure Not Platform"
**Source**: User correction during planning session
**Date**: 2025-06-30
**Clarification**: "EverArchive is a non-profit that doesn't touch money. It offers tools and services."
**Why It Matters**: Crystallized the entire model—we enable value, not extract it
**Implementation**: Complete pivot to infrastructure provider model, emphasis on concrete services

### Key Insight #4: "Deep Authorship"
**Source**: AI collaboration session
**Date**: 2024-07-20
**The Naming**: The moment when scattered ideas unified under one concept
**Significance**: Gave language to the deeper mission beyond storage
**Result**: Transformed "preservation project" into a movement

### Key Insight #5: "Lighthouse Is the Weak Link"
**Source**: Technical architecture review
**Date**: 2025-02-15
**Realization**: Arweave's centralized gateway contradicts permanence promise
**Implication**: Must build open source alternative
**Action**: EverArchive Gateway became priority one

---

## Section 8: Complete Source Document Index

### Primary Vision Documents

```
Path: /♾️ EverArchive/📦 Archive/EverArchive (was Forever Sites)/👉 EverArchive - Consolidation - Round 1/7. EverArchive - ESSENCE & VISION.md
Date: 2024-11-30
Version: 1.0
Status: AUTHORITATIVE - Pure original vision
Key Excerpts:
- "We are not building a better backup. We are building a better memory."
- "To be remembered is a fundamental human need."
- "The creator must have absolute sovereignty over their own memory."
- "Preserve the process, not just the product."
- "Non-extractive sustainability"
Used For: Genesis story, core philosophy, north star principles

Path: /♾️ EverArchive/📚 Canonical Library/Documentation/01 - Weighted Insights.md
Date: 2025-06-18  
Version: 0.1
Status: AUTHORITATIVE - Distilled principles
Key Excerpts:
- "Creator sovereignty & zero-knowledge encryption are non-negotiable"
- "Write for readers 500 years ahead"
- "Maintain complexity; avoid reductive bullet-pointing"
Used For: Core principles, user research insights

Path: /🧑🏻‍💻 Redesign-2025/DC-Website-Migration-2025/02-everarchive.org.md
Date: 2025-06-30
Version: Current
Status: CORRECTED - Infrastructure vision restored
Key Excerpts:
- Complete "What We Are/Aren't" section
- "Non-profit civilizational memory infrastructure"
- Infrastructure services detailed
Used For: Current positioning, concrete services
```

### Vision Evolution Documents

```
Path: /♾️ EverArchive/📚 Canonical Library/Tome I - The Vision/1.1 - The EverArchive Manifesto.md
Date: 2025-06-19
Version: 3.1
Status: REQUIRES CORRECTION - Contains monetization language
Issues: "Process monetization," "earning income," commercial framing
Action: Complete rewrite needed

Path: /♾️ EverArchive/📋 Active Work/Round 2/03 - Comprehensive Benefits Analysis/
Date: June 2025
Status: REQUIRES CORRECTION - Commercial benefits framing
Issues: "3-5x value premium," revenue language throughout
Action: Reframe all 47+ benefits as infrastructure enabling
```

### Rough Notes and Early Concepts

```
Path: /📦 Archive/💬⚒ Conversation Processing/
Content Type: AI collaboration logs
Significance: Contains Deep Authorship naming moment, three-layer model synthesis
Status: Historical reference, vision evolution

Path: /📦 Archive/Legacy Forever Sites (Oct 2024)/
Content Type: Original project documents
Significance: Shows pre-EverArchive thinking, early non-profit intention
Status: Historical reference only
```

### Missing Documents Located

During this review, we expected but didn't find:
1. **Original whitepaper draft** - Only Draft 1 exists
2. **Board meeting notes** - May be in private records
3. **Grant applications** - Would show early non-profit framing
4. **Investor feedback** - Would reveal pressure sources

### Document Conflicts Resolved

```
Conflict: Benefits list monetization vs. infrastructure
Resolution: All benefits reframed as infrastructure enabling
Example: "Earn from process" → "Enable process-based value"

Conflict: Platform vs. infrastructure positioning  
Resolution: Clear infrastructure provider model
Example: "Users on platform" → "Creators using tools"

Conflict: Manifesto commercial language
Resolution: Complete rewrite required
Example: Remove all "monetization" references
```

---

## Section 9: Red Flag Scanning Checklist

### Quick Scan Points
- [ ] Any mention of payment processing? **NO**
- [ ] Any marketplace language? **NO**
- [ ] Any revenue sharing concepts? **NO**
- [ ] Any platform (vs infrastructure) positioning? **NO**
- [ ] Any ownership of user content implications? **NO**
- [ ] Any "earn money" promises? **NO**
- [ ] Any commercial platform features? **NO**
- [ ] Any investor-oriented language? **NO**
- [ ] Any growth hacking concepts? **NO**
- [ ] Any extractive business model elements? **NO**

### Language Verification
- [ ] All benefits framed as infrastructure? **YES**
- [ ] Services clearly non-commercial? **YES**
- [ ] Non-profit status prominent? **YES**
- [ ] Creator sovereignty emphasized? **YES**
- [ ] Open source commitment clear? **YES**

### Conceptual Alignment
- [ ] Infrastructure provider positioning? **YES**
- [ ] Tools and protocols focus? **YES**
- [ ] Community governance model? **YES**
- [ ] Endowment sustainability? **YES**
- [ ] Mission-driven language? **YES**

---

## Section 10: Lessons Learned & Future Protection

### How Vision Drift Happened

1. **Investor Pitch Pressure** (Q4 2024)
   - Need to show "business model"
   - Revenue projections requested
   - Comparison to for-profit platforms

2. **Competitor Language Adoption** (Q1 2025)
   - Using their terminology unconsciously
   - "Marketplace" seemed normal
   - Platform thinking infiltrated

3. **Feature Creep** (Q1-Q2 2025)
   - "Wouldn't it be cool if..."
   - Adding marketplace to compete
   - Mission dilution through expansion

4. **Abstract Benefits** (Q2 2025)
   - Without concrete services, filled with promises
   - "Enable value" became "capture value"
   - Benefits detached from infrastructure

### Early Warning Signs (Watch For These)

- When "revenue" enters conversations
- When "platform" replaces "infrastructure"  
- When "users" become "customers"
- When "value" means "money" not impact
- When urgency overrides mission alignment
- When features don't trace to core vision
- When benefits sound like sales pitches

### Protection Protocols

1. **Monthly Vision Alignment Reviews**
   - Check all new content against Sacred Vision
   - Flag any drift immediately
   - Course correct before momentum builds

2. **All Changes Require Vision Check**
   - New features must enhance infrastructure
   - Benefits must enable, not extract
   - Language must match approved terminology

3. **New Team Member Onboarding**
   - Read Sacred Vision Document first
   - Understand infrastructure model
   - Learn forbidden language list

4. **Quarterly Stakeholder Alignment**
   - Board reviews vision adherence
   - Community provides feedback
   - Adjustments only with unanimous approval

5. **Annual Vision Renewal Ceremony**
   - Re-read genesis story together
   - Recommit to infrastructure model
   - Update protection mechanisms

### Vision Keeper Responsibilities

The Vision Keeper role (rotating annually) must:
- Review all public-facing content monthly
- Challenge revenue-oriented thinking
- Maintain infrastructure focus in planning
- Protect creator sovereignty in features
- Ensure non-profit alignment in partnerships
- Guard against platform language creep
- Document any attempted vision changes

---

## Appendices

### A. Complete Red Flag Word List

**Primary Forbidden Terms**:
- Monetize, Monetization, Monetizing
- Marketplace, Market, Trading
- Payment, Transaction, Process payments
- Revenue, Profit, Earnings, Income (in context of EverArchive generating)
- Customer, Consumer, Buyer
- Pricing, Price point, Tier pricing
- Commission, Fee, Percentage, Cut
- Seller, Vendor, Merchant

**Secondary Watch Terms** (context-dependent):
- Platform (when referring to EverArchive)
- User (prefer "creator" or "institution")
- Growth (prefer "adoption" or "impact")
- Scale (ensure refers to infrastructure, not revenue)
- Value (ensure means "enable" not "capture")

### B. Approved Language Guide

| Instead of... | Say... |
|---------------|--------|
| "monetize your process" | "preserve your process for future opportunities" |
| "marketplace for creators" | "infrastructure for creative preservation" |
| "payment processing" | "attribution infrastructure" |
| "revenue stream" | "sustainability funding" |
| "customer acquisition" | "creator adoption" |
| "pricing tiers" | "service levels" |
| "platform users" | "creators and institutions using our tools" |
| "value capture" | "value enablement" |

### C. Vision Check Questions

Before approving any new initiative, ask:

1. Does this serve creators without extracting value?
2. Is this infrastructure or a platform feature?
3. Does this require handling money?
4. Does this increase creator sovereignty?
5. Can this survive without EverArchive the organization?
6. Does this align with non-profit status?
7. Would this compromise zero-knowledge principles?
8. Does this preserve process or just product?

### D. Historical Correction Log

- **2025-06-30**: Removed all "marketplace" language from website plan
- **2025-06-30**: Changed "earn money" to "enable value" throughout
- **2025-06-30**: Replaced "platform" with "infrastructure" globally
- **2025-06-30**: Corrected 47+ benefits from monetization to enabling
- **2025-06-30**: Added concrete services (Gateway, Nodes, Professional)
- **2025-06-30**: Created Sacred Vision Document for permanent reference

### E. Stakeholder Commitment Records

- Board approval: [Pending]
- Team alignment: [Pending]
- Advisor acknowledgment: [Pending]
- Community endorsement: [Pending]
- Vision Keeper appointed: [Pending]

---

## Final Declaration

This Sacred Vision Document represents the authentic, undistorted mission of EverArchive. Born from personal pain and civilizational need, our purpose is to build infrastructure ensuring no human voice is lost to the void.

We are not a platform. We are not a marketplace. We do not touch money.

We build tools. We maintain networks. We provide services. We enable others to create value from preserved human creativity.

The ghost of the forgotten artist drives us forward. The roar of the coming AI flood makes our work urgent. But we build for centuries, not quarters.

**We are not building a better backup. We are building a better memory.**

This vision is sacred. Guard it well.

---

*For approval by: [User/Board/Stakeholders]*
*Once approved, this document will be locked and require unanimous consent to modify.*