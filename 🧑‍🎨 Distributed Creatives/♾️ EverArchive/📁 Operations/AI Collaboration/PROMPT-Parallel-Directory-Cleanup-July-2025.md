# Parallel Directory Cleanup Prompt
**Created**: July 3, 2025  
**Purpose**: Enable multiple AI agents to clean directories in parallel  
**Target Directories**: Active Work, Publications, Docs, xNOTES

---

## Your Mission

You are assigned to lead the cleanup of **[DIRECTORY_NAME]** in the EverArchive project. Your goal is to transform a potentially chaotic directory into a clean, organized, and purposeful structure that supports forward progress rather than endless analysis.

## Initial Setup

### 1. Read Project Rules
Start by reading these essential files to understand the project context:
- `/CLAUDE.md` (root directory) - Project-wide rules
- `/♾️ EverArchive/CLAUDE.md` - EverArchive-specific rules
- `/📁 Operations/Team Handovers/2025-07-03-10-00-handover-current-state-and-audit-needs.md` - Current state

### 2. Understand the Cleanup Philosophy
Based on recent lessons learned:
- **Progress over perfection** - 85% clean is better than 35% analyzed
- **No language obsession** - Don't hunt for contamination
- **Forward focus** - Keep what helps build, archive what doesn't
- **Clear organization** - Everything should have an obvious home

## Directory-Specific Instructions

### For Active Work Directory
**Primary Goal**: Separate current work from completed/abandoned efforts

**Keep**:
- Current development work (Round 2, current sprint materials)
- Active collaboration documents (Archive.org materials)
- Forward-looking plans and strategies
- Work that directly supports website launch, white paper, or conference

**Archive**:
- Completed rounds with no active work
- Language cleanup sprints (already identified)
- Abandoned initiatives
- Analysis documents that led nowhere

**Create**:
- Clear index showing what's actively being worked on
- README files in major subdirectories explaining their purpose

### For Publications Directory
**Primary Goal**: Ready-to-publish materials clearly separated from drafts

**Keep**:
- Final or near-final website content
- White paper generation scripts and latest versions
- Conference materials for upcoming events
- Templates for future publications

**Archive**:
- Old drafts superseded by newer versions
- Abandoned publication attempts
- Materials for past events

**Create**:
- Clear structure: `/drafts/`, `/final/`, `/templates/`
- Status indicators (DRAFT, REVIEW, FINAL) in filenames

### For Docs Directory
**Primary Goal**: Clear separation of different documentation types

**Expected Structure**:
```
docs/
├── changelogs/      # Keep recent, archive old
├── handovers/       # Keep current, archive superseded
├── guides/          # Operational how-tos
├── templates/       # Reusable document templates
└── archive/         # Old/superseded documents
```

**Archive Criteria**:
- Changelogs older than 1 month
- Superseded handovers
- Duplicate documentation
- Analysis documents about analysis

### For xNOTES Directory
**Primary Goal**: Determine if this is active notes or legacy content

**Investigation Needed**:
- Is this current working notes or old material?
- Does it duplicate content elsewhere?
- Is it referenced by other active documents?

**Likely Actions**:
- If legacy: Consider archiving entire directory
- If active: Organize by topic/date, remove duplicates
- Create clear README explaining purpose

## Cleanup Process

### Step 1: Create Sub-Agents
You MUST create sub-agents for parallel execution:

```markdown
## Sub-Agent 1: Content Auditor
**Task**: Scan all files and create inventory
**Output**: List of files with classifications (keep/archive/unclear)
**Time**: 30 minutes

## Sub-Agent 2: Archive Executor  
**Task**: Move identified files to archive
**Output**: List of archived files and reasons
**Time**: 20 minutes

## Sub-Agent 3: Structure Creator
**Task**: Create new organizational structure
**Output**: New directory structure with READMEs
**Time**: 30 minutes

## Sub-Agent 4: Index Generator
**Task**: Create directory index and documentation
**Output**: Updated index files
**Time**: 20 minutes
```

### Step 2: Define "Clean" Directory
A clean directory has:
1. **Clear purpose** - Obvious what it's for from structure
2. **No duplicates** - Each document has one authoritative location
3. **Current content** - Old/superseded materials archived
4. **Good navigation** - Index or README at each level
5. **Forward focus** - Supports building, not analyzing

### Step 3: Archive Strategy
Create archive structure:
```
📦 Archive/2025-07-[Directory]-Cleanup/
├── ARCHIVE-SUMMARY.md
├── language-obsession/
├── abandoned-work/
├── superseded-content/
└── duplicate-files/
```

## Reporting Requirements

### Create Final Report
After cleanup, create:
`/📁 Operations/Project History/2025-07-03-[directory]-cleanup-complete.md`

Include:
1. **Before State**: Brief description of issues found
2. **Actions Taken**: What was archived and why
3. **After State**: New structure and organization
4. **Key Improvements**: How this helps forward progress
5. **Remaining Questions**: Any unclear items needing human decision

### Update Directory Index
Update or create:
`/[Directory]/00 - [Directory] Index.md`

With:
- Current structure
- Purpose of each subdirectory
- Quick reference for common tasks
- What was archived and where

## Coordination Protocol

Since multiple agents work in parallel:
1. **Don't touch other directories** - Stay in your assigned area
2. **Document cross-references** - Note if you find links to other directories
3. **Flag shared content** - Don't move files referenced by multiple directories
4. **Time-box work** - Complete within 2 hours

## Success Criteria

Your cleanup succeeds if:
1. ✅ Directory has clear, logical structure
2. ✅ No duplicate or obsolete files in main areas
3. ✅ Navigation is simple and obvious
4. ✅ Archives preserve history without cluttering present
5. ✅ Next person can understand structure in 5 minutes

## Assignment Template

When assigning to agents, use:
```
Please execute the Parallel Directory Cleanup Prompt for the [DIRECTORY_NAME] directory.

Location: /♾️ EverArchive/[DIRECTORY_PATH]/
Priority Focus: [Specific concerns for this directory]
Special Instructions: [Any directory-specific needs]

Create sub-agents as specified and complete within 2 hours.
```

---

**Remember**: The goal is a clean, usable directory structure that helps build EverArchive infrastructure. Perfect organization at 35% completion is worse than good-enough organization at 100% completion.