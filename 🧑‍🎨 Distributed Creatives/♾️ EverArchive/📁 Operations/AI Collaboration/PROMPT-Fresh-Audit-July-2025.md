# Fresh Audit Prompt for EverArchive
**Created**: July 3, 2025  
**Purpose**: Enable clean, practical audit without historical bias  
**Focus**: Identify real blockers to publishing priorities

---

## Your Mission

Conduct a fresh, independent audit of EverArchive to determine what's actually blocking progress on key deliverables. Focus on practical findings, not perfect analysis.

## Context You Need

**Project**: EverArchive - infrastructure for preserving creative process (non-profit)  
**Current Goals**: Launch website, create white paper, prepare for conference panel  
**Key Issue**: Previous agents may have over-focused on language problems  
**Your Job**: Find out what's REALLY blocking progress

## Specific Tasks

### 1. Website Launch Readiness (30 minutes max)
Check `/📖 Publications/Website Content/`:
- [ ] Does clean, publishable content exist?
- [ ] Any obvious infrastructure vs. platform issues?
- [ ] What specifically blocks launch today?
- [ ] Quick fixes possible? (Don't do them, just identify)

### 2. White Paper Feasibility (20 minutes max)
Check `/📚 Canonical Library/`:
- [ ] Are the 4 Tomes reasonably complete?
- [ ] Any major gaps for white paper creation?
- [ ] User has process for this - what content is ready?

### 3. Conference Materials (15 minutes max)
Check `/📋 Active Work/Archive.org Collaboration/`:
- [ ] What materials exist for Brewster panel?
- [ ] Are they aligned with infrastructure message?
- [ ] What's missing for conference presentation?

### 4. Claimed Deliverables Verification (15 minutes max)
Quickly verify if these claimed items actually exist:
- [ ] Manifesto v4.1 (supposedly cleaned)
- [ ] EVERARCHIVE-CANONICAL-COMPLETE.md
- [ ] Brewster meeting materials
- [ ] Website content files

### 5. Real Blockers Summary (10 minutes)
Based on findings:
- What actually prevents website launch?
- What actually prevents white paper creation?
- What actually prevents conference readiness?

## Important Guidelines

### DO:
- Check actual files yourself
- Focus on publishing blockers
- Time-box each section
- Be practical, not perfect
- Identify quick wins

### DON'T:
- Rely on previous audits' percentages
- Hunt for language issues obsessively
- Create elaborate plans
- Analyze what others did
- Seek perfection

### Language Note:
- Use terms like "issues", "problems", "misalignment"
- Avoid the c-word that rhymes with "contamination"
- Focus on infrastructure vs. platform distinction

## Output Format

Provide a brief, practical report:

```markdown
# EverArchive Publishing Readiness Audit
Date: [Date]
Time Spent: [Should be under 2 hours]

## Website Launch Status
**Ready?** Yes/No
**Blockers**: [List specific issues]
**Quick fixes**: [If any, under 1 hour]

## White Paper Status  
**Ready?** Yes/No
**Missing**: [List specific gaps]
**Time to ready**: [Estimate]

## Conference Status
**Ready?** Yes/No  
**Needs**: [List specific items]

## Verified Deliverables
[✓ or ✗ for each claimed item]

## Priority Actions
1. [Most important blocker]
2. [Second blocker]
3. [Third blocker]

## Notes
[Any critical observations]
```

## Success Criteria

Your audit succeeds if it:
1. Takes less than 2 hours
2. Identifies real blockers (not theoretical issues)
3. Focuses on publishing priorities
4. Provides actionable findings
5. Avoids analysis paralysis

## Remember

The user wants to PUBLISH, not perfect. They have:
- A website ready to show you
- A white paper process ready to use  
- A conference deadline approaching
- Fatigue from over-analysis

Help them ship. Find what's actually blocking progress, not what could be better in an ideal world.

---

*Start with: `/📁 Operations/Team Handovers/2025-07-03-10-00-handover-current-state-and-audit-needs.md` for session context*