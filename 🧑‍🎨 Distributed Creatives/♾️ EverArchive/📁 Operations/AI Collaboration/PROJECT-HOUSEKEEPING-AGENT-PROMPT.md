# Project Housekeeping Agent Prompt

**Agent Task**: EverArchive Project Status Documentation and Cleanup  
**Date**: July 5, 2025  
**Context**: Transition from Features Integration to Website Development  

---

## YOUR MISSION

You are tasked with documenting the completion of the Features Integration phase and cleaning up project documentation to reflect the current state as we transition to Website Development for the July 10 conference.

## IMMEDIATE TASKS

### 1. **Document Phase Completion**
- Mark the Features Integration work as **COMPLETE** in project status documents
- Update any project trackers or status files to reflect transition to Website Development phase
- Create a clear record that the Benefits → Features transformation is finished

### 2. **Update Project Status Documentation**
- Review and update `📁 Operations/AI Collaboration/EVERARCHIVE-PROJECT-CONTEXT.md`
- Ensure it reflects current state: Features work complete, Website development active
- Update any project timelines or phase documentation

### 3. **Clean Up Active Work Directory**
- Review `📋 Active Work/05-PHASE-5-FEATURES-UPDATES/` and determine what should be archived
- Move completed Features Discovery work to appropriate archive locations
- Ensure no orphaned or confusing documentation remains in active areas

### 4. **Archive Completed Work**
- Move completed Features Integration materials to `📦 Archive/` with appropriate dating
- Preserve the Features Discovery Results for reference
- Clean up any temporary or draft materials that are no longer needed

### 5. **Update Project Navigation**
- Ensure project index documents reflect current priorities (Website Development)
- Update any README files or navigation documents
- Make it clear to future agents what phase we're in and what's been completed

## CURRENT PROJECT STATE (For Your Reference)

### ✅ **COMPLETED PHASES**
- **Benefits Framework** → Transformed into comprehensive Features library
- **Features Discovery** → 92+ features identified and documented across 8 categories
- **Features Integration** → All new features integrated into existing structure
- **Features Organization** → Complete directory structure with 1-8 categories populated

### 🔄 **CURRENT ACTIVE PHASE** 
- **Website Development** → Organized in `📁 Operations/Website/` with complete process documentation
- **Conference Preparation** → July 10, 2025 deadline (5 days)

### 📍 **KEY LOCATIONS**
- **Features Library**: `📚 Canonical Library/Features/` (COMPLETE - 92+ features)
- **Website Development**: `📁 Operations/Website/` (ACTIVE)
- **Project Context**: `📁 Operations/AI Collaboration/EVERARCHIVE-PROJECT-CONTEXT.md`
- **Handovers**: Latest website handover at `docs/handovers/2025-07-05-20-00-handover-EverArchive-Website-Development.md`

## SUCCESS CRITERIA

When you're done, any agent coming into the project should be able to:
1. **Quickly understand** that Features work is complete and archived
2. **Clearly see** that Website Development is the current active priority
3. **Find clean documentation** without confusion about what phase we're in
4. **Access current work** in `📁 Operations/Website/` without digging through old materials

## DELIVERABLE

Create a summary document in `📁 Operations/Project History/` titled `2025-07-05-Phase-Transition-Features-to-Website.md` that documents:
- What was completed in the Features phase
- What was moved/archived and where
- Current project status and active work location
- Clear guidance for future agents on project state

---

**Remember**: Work within the EverArchive directory only. This is housekeeping and organization work, not content creation. Focus on clarity and project navigation for future agents.