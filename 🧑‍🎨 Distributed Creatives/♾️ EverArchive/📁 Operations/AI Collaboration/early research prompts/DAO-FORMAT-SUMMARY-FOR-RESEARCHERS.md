# .dao Format Summary for External Researchers

**Purpose**: This document explains the .dao (Decentralized Authorship Object) format to provide context for research tasks. The .dao is our custom digital preservation format - NOT to be confused with DAOs (Decentralized Autonomous Organizations).

## What is a .dao file?

A .dao file is a ZIP-compressed archive containing a creative work organized in three layers, designed for long-term digital preservation with creator control and process documentation.

## Three-Layer Structure

### 1. Core Layer (Encrypted)
- The actual creative work (documents, code, art, etc.)
- Encrypted with creator-controlled keys
- Includes metadata about authorship and permissions
- Zero-knowledge encryption ensures only the creator can access

### 2. Process Layer
- Complete creation history (like Git for any creative work)
- Version history, edits, branches, annotations
- Comments, notes, and collaboration records
- Creation timeline and workflow documentation
- Not encrypted - preserves the "how" of creation

### 3. Surface Layer (Public)
- Public previews and discovery metadata
- Thumbnails, abstracts, descriptions
- Social interactions and citations
- Everything needed for search and discovery
- Unencrypted for public access

## Technical Structure

```
creative-work.dao/
├── manifest.json          # Entry point with integrity data
├── metadata/             
│   ├── authorship.json    # Creator information
│   ├── permissions.json   # Access controls
│   └── context.jsonld     # Semantic metadata
├── core/                  # Encrypted creative work
│   └── [encrypted files]
├── process/               # Creation history
│   └── [version history, annotations]
└── surface/               # Public metadata
    └── [previews, descriptions]
```

## Key Characteristics

- **Self-contained**: All data needed for interpretation is included
- **Sovereign**: Creators control their own encryption keys
- **Immutable**: Designed for permanent storage on decentralized networks
- **Interoperable**: Uses open standards (JSON-LD, ZIP) for compatibility
- **Process-preserving**: Captures not just the final work but how it was created

## Why This Matters for Research

When researching metadata standards, key management, or preservation systems, understand that we need to:

1. **Map this structure to existing standards** (METS, MODS, Dublin Core) without losing the three-layer separation
2. **Preserve creative process data** which traditional archives typically don't capture
3. **Maintain creator sovereignty** through encryption while enabling institutional integration
4. **Support permanent storage** with cryptographic verification

## What Makes .dao Different

Traditional digital preservation focuses on final outputs. The .dao format preserves:
- Every edit and revision
- Creative decisions and dead ends
- Collaboration and feedback
- The emotional and cognitive journey
- Creator control over access

This is why standard metadata schemas may not fully represent our needs - they weren't designed for process preservation or creator-controlled encryption.

## For Research Purposes

When researching external systems, consider:
- How they handle versioning and process documentation
- Their approach to creator rights and access control
- Integration patterns with existing standards
- Long-term sustainability and verification mechanisms

The goal is to learn from existing systems to inform our implementation, not to assume external researchers understand our specific format.