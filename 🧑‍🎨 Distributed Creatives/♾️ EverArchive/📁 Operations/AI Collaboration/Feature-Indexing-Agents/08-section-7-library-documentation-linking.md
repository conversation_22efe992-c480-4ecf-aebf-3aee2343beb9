# AGENT 2G: Section 7 Library Documentation Linking

**Agent Type**: Documentation Content Specialist  
**Scope**: Section 7 - Library & Book Ecosystem (7 features)  
**Focus**: NON-FEATURE documentation content linking and verification  

## MISSION

Link all Section 7 features to their referenced canonical documentation (EXCLUDING other features) and verify all content claims.

## WORKING DIRECTORY
`/Users/<USER>/work/obsidian-vault/🧑‍🎨 Distributed Creatives/♾️ EverArchive/`

## TARGET FEATURES
- All features in `📚 Canonical Library/Features/7-Library-Book-Ecosystem/`
- Subdirectories: 7.1-User-Experience, 7.2-Lending-Infrastructure

## DOCUMENTATION LINKING SCOPE

**INCLUDE ONLY**:
- `📚 Canonical Library/Tome I/`, `Tome II/`, `Tome III/`, `Tome IV/` (non-features)
- `📋 Active Work/` directories (library partnerships, integration specs)
- `📁 Operations/` directories (technical specs, user experience)
- `📖 Publications/` directories (library documentation)
- External library standards and systems

**EXCLUDE COMPLETELY**:
- Any files in `📚 Canonical Library/Features/` (feature-to-feature links are Agent 1's job)
- Any `[[XX-feature-name]]` style references

## SEARCH PRIORITIES FOR SECTION 7

- **Library integration** - ILS and authentication system documentation in Tome II/Technical
- **NFT lending** - time-locked lending technical specifications in Operations/Technical
- **User experience** - DRM-free access documentation in Operations/UX
- **Circulation automation** - hold and waitlist management systems
- **Consortium support** - multi-library coordination documentation

## IMPORTANT CONSTRAINTS

- **Focus ONLY on Section 7 features**
- **COMPLETELY IGNORE feature-to-feature links** (Agent 1's job)
- **Do NOT link to any files in Features directory**
- **Prioritize library integration verification** (critical for library partnerships)
- **Verify all technical library system claims**
- **Check for actual ILS integration documentation**