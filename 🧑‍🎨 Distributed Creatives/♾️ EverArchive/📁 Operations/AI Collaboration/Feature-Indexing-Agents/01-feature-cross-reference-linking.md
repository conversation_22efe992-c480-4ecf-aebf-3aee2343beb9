# AGENT 1: Feature Cross-Reference Linking

**Agent Type**: Feature Cross-Reference Specialist  
**Scope**: All 92+ features across sections 1-8  
**Focus**: Feature-to-feature linking only  

## MISSION

Add proper front matter and cross-reference linking to ALL features in the EverArchive Features directory.

## WORKING DIRECTORY
`/Users/<USER>/work/obsidian-vault/🧑‍🎨 Distributed Creatives/♾️ EverArchive/📚 Canonical Library/Features/`

## SPECIFIC TASKS

1. **Read every single feature file** (all 92+ features across sections 1-8)
2. **Add proper YAML front matter** to each feature including metadata and cross-references
3. **Convert all "Feature XX" references** to proper Obsidian links like [[XX-feature-name]]
4. **Add "Referenced Features" section** with proper internal links
5. **Verify all feature cross-references** are accurately linked

## FRONT MATTER FORMAT
```yaml
---
feature_id: XX
title: "Feature Name"
category: "X.X Category Name"
status: "Status from inventory"
cross_references:
  features:
    - "[[01-biometric-proof-creative-origin]]"
    - "[[42-legal-evidence-infrastructure]]"
  internal_links: true
---
```

## LINKING RULES

- Find all mentions of "Feature XX" in content
- Convert to [[XX-feature-name]] format using actual filenames
- Use exact filename without .md extension
- All links are INTERNAL (within EverArchive)
- Mark link_type as "feature_reference"

## VERIFICATION REQUIREMENTS

- Verify every linked feature file actually exists
- Report any broken feature references
- Ensure bidirectional linking is possible
- Create list of any missing feature files

## DELIVERABLE

All 92+ features updated with proper front matter and feature cross-reference linking.

## IMPORTANT CONSTRAINTS

- **ONLY work on feature-to-feature links**
- Do NOT attempt to link to documentation content
- Focus exclusively on "Feature XX" style references
- Ensure all links use actual existing filenames