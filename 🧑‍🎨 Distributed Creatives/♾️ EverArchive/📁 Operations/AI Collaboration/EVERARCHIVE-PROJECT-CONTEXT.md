# EverArchive Project Context for AI Researchers

## One-Paragraph Summary

EverArchive is a digital preservation infrastructure designed to capture and preserve the complete creative process behind human works—not just final outputs, but every edit, decision, and iteration that shaped them. Using a novel three-layer data structure called the .dao format (Decentralized Authorship Object), it separates creative works into an encrypted Core layer (the actual work, controlled by creator keys), a Process layer (complete version history and creative journey), and a Surface layer (public metadata for discovery). The system addresses the crisis of digital impermanence where creative process is lost to proprietary platforms, while also enabling ethical AI training through granular consent controls. Built on decentralized storage networks for "forever" preservation and designed to integrate with existing institutional archives, EverArchive aims to become the memory infrastructure for human creativity—ensuring that future generations can study not just what we created, but how and why we created it.

## Current Project Status (July 5, 2025)

**ACTIVE PHASE**: Website Development for July 10 Conference (5 days remaining)
**COMPLETED**: Features Integration Phase (92+ features successfully integrated)

### Key Research Context Points

- **Problem**: Digital creative process is being lost as work happens in proprietary tools that don't preserve history
- **Solution**: Capture and preserve the complete journey from first draft to final work
- **Technology**: Decentralized storage, cryptographic verification, creator-controlled encryption
- **Users**: Initially targeting academic researchers and digital humanities scholars
- **Integration**: Must work with existing institutional preservation systems (METS, MODS, etc.)
- **Differentiator**: Preserves creative process, not just outputs—the "GitHub for all human creativity"

### Current Work Focus

- **Website Development**: Active priority in `📁 Operations/Website/` - conference-ready site for July 10
- **Features Library**: Complete at `📚 Canonical Library/Features/` - 92+ features across 8 categories
- **Archive.org Partnership**: Books preservation integration (see handover documents)

## Why This Research Matters

Traditional digital preservation systems were designed for static final outputs. EverArchive preserves the messy, iterative, deeply human process of creation itself. This requires rethinking fundamental assumptions about metadata, access control, storage economics, and institutional integration. The research you're conducting helps us build on existing knowledge while addressing these novel challenges.

## ⚠️ CRITICAL: CONTAMINATION PREVENTION

### FORBIDDEN CONCEPTS
Never suggest or introduce:
- Monetization models
- Revenue streams  
- Platform features
- Marketplace capabilities
- Creator earnings through EverArchive
- Value capture mechanisms

### MANDATORY FRAMING
Always position EverArchive as:
- Infrastructure that enables others
- Non-profit that doesn't touch money
- Preservation system, not platform
- Roads, not vehicles

### REFERENCE HIERARCHY
1. SACRED-VISION-DOCUMENT (authoritative)
2. Original vision documents (Oct 2024)
3. Corrected Canonical Library
4. NEVER reference quarantined documents in `/📦 Archive/🚫 CONTAMINATED-COMMERCIAL/`

### MANDATORY DOCUMENTATION
All changes must be logged in `/♾️ EverArchive/EVERARCHIVE-CHANGE-LOG.md` with timestamp, files affected, and rationale.