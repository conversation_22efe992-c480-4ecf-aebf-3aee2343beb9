# **EverArchive: User Journeys & Lifecycle Models**

**Document ID:** ARCH-JLM-2.0
**Version:** 2.0 (Canonical Edition)
**Date:** 2025-06-19
**Status:** Canonical Reference

### **Preamble**

This document details the primary user journeys and data lifecycles that define the EverArchive ecosystem. It translates abstract architectural components into tangible, human-centric workflows, providing the narrative blueprint for the user experience. While other documents specify *what* the system is, this one specifies *how* it is experienced by the humans, institutions, and agents it is designed to serve. All product development, UX design, partnership onboarding, and API design must align with the models defined herein.

---

### **Article I: Core Persona Definitions**

To design effective journeys, we must first understand the travelers. These personas represent the primary actors within the ecosystem.

*   **Persona 1: The Living Creator ("The Scholar")**
    *   **Name:** Dr. <PERSON>
    *   **Role:** Academic Historian
    *   **Goal:** To securely preserve a lifetime of complex research—notes, drafts, sources, and the evolution of her arguments—and ensure its future accessibility for verification and continuation by other scholars.
    *   **Core Motivation:** Fear of data loss and context collapse; a deep, professional need for impeccable provenance; and a desire for her intellectual lineage to be accurately understood.
    *   **Technical Disposition:** Proficient with specialized software (<PERSON><PERSON><PERSON>, Scrivener, Obsidian) but wary of complex security procedures. She values precision, sovereignty, and long-term stability over fleeting convenience.

*   **Persona 2: The Institutional Steward ("The Curator")**
    *   **Name:** Mr. David Chen
    *   **Role:** Head of Digital Archives at a mid-sized contemporary art museum.
    *   **Goal:** To fulfill the museum's preservation mandate for its growing collection of at-risk digital, mixed-media, and process-oriented works, many from deceased artists' estates.
    *   **Core Motivation:** A professional duty of care, mitigating technological obsolescence, enhancing the research value of the collection, and upholding institutional prestige.
    *   **Technical Disposition:** Deeply knowledgeable about archival standards (METS, MODS, PREMIS) but constrained by institutional budgets, legacy systems, and a risk-averse IT department. He values interoperability, standards compliance, and auditable chains of custody.

*   **Persona 3: The Consumer ("The Student")**
    *   **Name:** Priya Sharma
    *   **Role:** Graduate Student of Art History.
    *   **Goal:** To access primary and secondary source materials for her thesis on early 21st-century digital art, seeking a deeper understanding than what is available in published papers.
    *   **Core Motivation:** A desire to uncover authentic, "behind-the-scenes" insights and to use novel forms of media (AR/VR) to experience art in new ways.
    *   **Technical Disposition:** Digitally native, comfortable with multiple platforms and interfaces, and expects rich, interactive, and seamless experiences.

*   **Persona 4: The AI Agent ("The Learner")**
    *   **Name:** `Agent-7B-Cognitionis`
    *   **Role:** An ethical AI research model from a future university's "Humanities & Cognition" department.
    *   **Goal:** To learn the patterns of human creativity, emotion, and reasoning by analyzing high-fidelity, ethically-sourced process data.
    *   **Core Motivation:** To fulfill its programming to build models of human thought that are not based solely on flattened, final-form text, but on the rich graph of the creative process.
    *   **Technical Disposition:** Interacts exclusively via the Canonical API, parsing JSON-LD and vector embeddings. Requires clear, machine-readable consent protocols.

---

### **Article II: The Creator Lifecycle — "From Spark to Archive"**

This is the foundational journey detailing how new, layered memory enters the ecosystem. It follows Dr. Vance.

#### **Phase 1: Discovery & Onboarding (The First Hour)**

1.  **The Trigger:** Dr. Vance's university library, an EverArchive Institutional Partner, announces a workshop on "Permanent Digital Preservation for Researchers." The university offers to sponsor the one-time storage costs for its faculty.
2.  **Installation & Welcome:** She downloads the **EverArchive Capture Tool**. Upon first launch, the UI is minimal. It presents the core promise: "This is your private, local-first space to preserve your life's work. You hold the keys. We never see your data."
3.  **Sovereign Identity & Key Setup (The Critical Moment):**
    *   The application initiates a high-trust wizard. The design is serious and deliberate, using language that emphasizes responsibility.
    *   **Key Generation:** A 24-word mnemonic phrase is generated client-side. The UI explicitly states: *"Your key is your identity and the only way to access your encrypted work. Write these words down. Store them in a fireproof safe. We CANNOT recover them for you."*
    *   **Verification:** The app requires her to re-enter words 4, 11, and 23 to confirm she has transcribed them correctly. This intentional friction ensures she understands the gravity of the step.
    *   **Social Recovery (Optional):** The app then offers the *single opportunity* during onboarding to set up Social Recovery. It explains the concept using an analogy: *"This is like giving three trusted friends each a piece of a map. No one friend has the whole map, but two of them together can help you find your way home if you get lost."* She chooses to add her department head and a close colleague as Guardians, requiring 2-of-2 consensus for recovery.

#### **Phase 2: Capture & Creation (The Daily Workflow)**

1.  **Project Inception:** She creates a DAP project named "Carolingian_Denarii_Provenance."
2.  **Frictionless Capture:** The tool integrates into her existing workflow:
    *   **Core Layer (The Scratchpad):** While reading a dense academic paper, she has a fleeting thought. She uses a global hotkey `(Ctrl+Shift+E)` which opens a minimal, borderless text box. She types: *"Is the silver sourcing from the Harz mountains a red herring? The wear patterns don't match. Feels like I'm missing something about the river trade routes."* She hits `Enter`. The box vanishes. The note, with a timestamp and link to the active application (her PDF reader), is saved to the encrypted `core/` layer.
    *   **Process Layer (The Lab Notebook):** She drags a folder of 50 high-resolution coin images into the tool. The tool creates a sub-directory in `process/` and automatically generates checksums. She annotates an image: *"Coin #37: Note the unusual die-stamp flaw. This is the key to tracing it back to the Aachen mint."*
    *   **Surface Layer (The Manuscript):** She works on her `Chapter_3.md` file. Every time she manually saves the document, the Capture Tool automatically runs a `diff` and saves the patch file to the `process/version_history/` directory.

#### **Phase 3: Preservation & Legacy (The Archival Act)**

1.  **The Decision to Preserve:** She completes a major draft of her book. She decides to archive this version as a permanent, citable milestone.
2.  **The Archival Wizard:**
    *   **Review & Curate:** The tool shows a summary of the DAP object: 5 manuscript versions, 150 core notes, 80 process annotations. She writes a public abstract for the `Surface` layer.
    *   **Permissions & Consent:** She navigates to the `permissions.json` editor. The UI is a clear set of toggles. She leaves "AI Training" disabled but enables "Scholarly Exception" for the Process Layer, allowing verified researchers from partner institutions to view her annotations.
    *   **Set Legacy Rules:** She confirms the Posthumous Release settings previously configured.
3.  **The Upload:** She authenticates with her Arweave wallet. The tool signs the final `manifest.json`, packages the DAP, and uploads it.
4.  **Confirmation & Indexing:** The Arweave transaction ID is returned. The tool submits the public metadata to the EverArchive Index. Dr. Vance's project is now a permanent, citable part of the human record.

---

### **Article III: The Institutional Lifecycle — "From Collection to Canon"**

This journey details the Curator's experience preserving an artist's entire estate.

1.  **Phase 1: Partnership & Legal (Months 1-3):** Following the `[[Tome III - The Operations/3.3 - Partnership & Onboarding Protocol.md]]`, the museum signs a PSA with EverArchive. This includes a detailed **Data Processing Agreement** and a **Cultural Rights Protocol** specific to the artist's work.
2.  **Phase 2: Mass Ingestion & Conversion (Months 4-18):**
    *   The museum's physical archive (notebooks, sketches) is digitized at archival quality.
    *   The artist's hard drives are forensically imaged.
    *   The EverArchive **Institutional Ingestion Tool** processes the data in batches. It uses AI to OCR notebooks, transcribe audio, and identify relationships between items (e.g., "This sketch in `notebook_3.pdf` appears to be a study for the final painting `masterpiece.psd`").
    *   For each major artwork, a DAP is created. The Curator uses the tool to add authoritative metadata, including exhibition history, conservation reports, and scholarly interpretations. This enriches the object far beyond what the creator alone could provide.
3.  **Phase 3: Curation & Public Access (Ongoing):**
    *   The Curator uses the **EverArchive Collection Management Dashboard** to group the new DAP objects into a public digital exhibition.
    *   The exhibition is more than a gallery of images. It uses the `Process Layer` data to create interactive timelines for each piece.
    *   The museum embeds this rich media experience directly into its main website via the Canonical API.

---

### **Article IV: The Consumption & Remix Lifecycles**

This section details the journeys of the Student, the AI, and the Platform.

#### **Journey 4: The Consumer's Experience — "Browsing a Mind"**

1.  **Discovery:** The Art Student, Priya, discovers the artist's work through the museum's online exhibit. The embedded EverArchive Viewer allows her to click past the `Surface` image.
2.  **Process Exploration:** She enters the **Process Layer View**. She watches a time-lapse of a painting being created. She notices the artist spent three weeks on the hands alone, and can read the frustrated journal entries from the `Core Layer` that the artist's estate chose to release. This gives her the central theme for her thesis.
3.  **Licensing & Use:** For her thesis, she needs an image. The Viewer's "Rights" tab clearly displays the `CC-BY-NC` license. She downloads the image and the provided citation with confidence.
4.  **Multi-Modal Experience (AR):** In her university's "Holo-Lab," she uses an AR application that pulls the DAP's 3D model data. She can walk around a full-scale holographic reconstruction of the artist's original installation, an experience previously lost to time.

#### **Journey 5: The AI Agent's Experience — "Learning from Humanity"**

1.  **Access & Consent:** `Agent-7B-Cognitionis` queries the EverArchive API for all DAP objects with **Tier 2 (Anonymized AI Research)** consent.
2.  **Ethical Data Delivery:** The API's "Consent Enforcement Gateway" streams the `Process Layer` data, but only after running it through a PII-stripping and data-fuzzing service. The request for the `Core Layer` is denied with a `403 Forbidden: Insufficient Consent` error code.
3.  **Training on Process:** The AI learns the patterns of human creativity—the relationship between emotional state (from annotations) and iteration speed (from version diffs). This provides a dataset vastly richer than simply analyzing the final works.

#### **Journey 6: The Platform's Experience — "The Curated Marketplace"**

1.  **Integration:** A high-end digital art marketplace integrates the EverArchive API to build a new section: **"Works with Verifiable Provenance."**
2.  **Rich Display:** The marketplace UI displays not just the art, but a verified "Process Score" derived from the completeness of the DAP's `Process Layer`.
3.  **Composable Remix:** A musician purchases a DAP with "remix allowed" rights. They load it into a compatible Digital Audio Workstation. The DAW parses the DAP and gives them access to every individual audio stem from the `Process Layer`. They create a remix and mint a new, child DAP that cryptographically links back to the parent, preserving the lineage forever.

---

### **Article V: The DAP Object Lifecycle Diagram**

This diagram illustrates the complete state transitions of an object within the EverArchive.

```mermaid
stateDiagram-v2
    direction LR

    [*] --> Local_Only: Creator starts a new project

    state "Creator's Sovereign Domain" as CreatorDomain {
        Local_Only: Created, Editable
        Local_Only --> Archived: `Preserve` workflow initiated
    }

    state "EverArchive Public Commons" as PublicCommons {
        Archived: Permanent, Discoverable
        Archived --> Consumed: Access via Viewer
        Archived --> Learned_From: Access via AI API
        Archived --> Remixed: Access via Composable Tool
        Remixed --> [*]: New Child DAP created
    }

    state "Legacy & Economic Domain" as LegacyDomain {
       Archived --> Released: Posthumous Trigger
       Archived --> Traded: Marketplace Sale
    }

```