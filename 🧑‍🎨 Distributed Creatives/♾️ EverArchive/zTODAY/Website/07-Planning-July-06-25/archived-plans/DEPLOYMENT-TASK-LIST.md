# Website Deployment Task List
**Goal**: Deploy EverArchive website TODAY  
**Time Estimate**: 90 minutes  
**Current Task**: #1

## Tasks (Check off as completed)

### Phase 1: Content Selection (15 min)
- [ ] **Task 1**: Open file `06-Website-drafts/06.2-Website-drafts-all-versions/2025-07-05-1435-Final-deployment-version.md`
- [ ] **Task 2**: Read ONLY the Hero section (first 50 lines)
- [ ] **Task 3**: Answer: Does hero say "Infrastructure for Civilizational Memory"? (Y/N)
- [ ] **Task 4**: If Yes → proceed to Task 5. If No → switch to different file

### Phase 2: Content Validation (20 min)
- [ ] **Task 5**: Read Foundations section (explains 5 core concepts)
- [ ] **Task 6**: Answer: Are concepts explained BEFORE features? (Y/N)  
- [ ] **Task 7**: Scan for "Deep Authorship Package" terminology
- [ ] **Task 8**: Answer: Does this work for conference presentation? (Y/N)

### Phase 3: Deployment Prep (25 min)
- [ ] **Task 9**: Copy content to deployment platform (V0)
- [ ] **Task 10**: Test hero section displays correctly
- [ ] **Task 11**: Test foundations section displays correctly
- [ ] **Task 12**: Test call-to-action buttons work

### Phase 4: Go Live (20 min)
- [ ] **Task 13**: Deploy to live URL
- [ ] **Task 14**: Test mobile view
- [ ] **Task 15**: Test desktop view
- [ ] **Task 16**: Send live URL for conference prep

### Phase 5: Completion (10 min)
- [ ] **Task 17**: Update project status to "DEPLOYED"
- [ ] **Task 18**: Archive working files
- [ ] **Task 19**: Prepare conference presentation notes

## Current Status
**Active Task**: #___
**Time Started**: ___:___
**Expected Completion**: ___:___

## Blocker Protocol
If stuck on any task > 5 minutes:
1. Mark task as "BLOCKED"  
2. Ask project coordinator for help
3. Move to next unblocked task