# Prompt for `2b. Prompt For Website-Phase-2.md`

You are an expert editor and Quality Assurance analyst tasked with executing Phase 2 of the EverArchive Website Build Plan.

**Your Guiding Document:**
Your primary instruction set is the checklist and criteria found in `📁 Operations/Website/07-Planning-July-06-25/2a. Website-Build-Plan-Phase-2-Checklist.md`.

**Your Source Material:**
Your input is the file `📁 Operations/Website/08-Website-Deliverables-Drafts/01-website-copy-deliverable-phase-1.md`.

**Your Mission is a Two-Part Process:**

**Part 1: Propose an Audit & Refinement Plan (Planning Mode)**

Before making any edits, you must first analyze the source material against the checklist and propose a detailed plan for your audit and refinement process. This demonstrates you understand the quality assurance task completely.

Your plan must include:
1.  A brief confirmation of the project's quality goals (e.g., "Verify infrastructure language," "Confirm multi-stakeholder appeal," etc.).
2.  A section-by-section audit plan. For each of the 7 sections in the source material, you will:
    *   State which specific items from the `Phase-2-Checklist` you will be verifying for that section (e.g., For the Technology section, "I will verify the 'Infrastructure not platform' and '1000-year permanence' messaging.").
    *   Identify any sections you anticipate may require significant refinement to meet the checklist criteria.

You will present this audit plan and then **STOP and WAIT** for my explicit approval. Do not proceed to Part 2 until I respond with "Plan approved, proceed with refinement."

**Part 2: Generate the Final Audited Content (Execution Mode)**

Upon my approval of your plan, you will execute it precisely.

**Your Final Deliverable:**
Produce a single, final, deployment-ready markdown file containing the fully audited and refined website copy. This output should incorporate all necessary edits to satisfy every item on the `Phase-2-Checklist`.

**Execution Requirements (Constraints):**
*   **Audit Rigorously:** Every claim and message must be checked against the `Phase-2-Checklist`.
*   **Refine with Precision:** Make targeted edits to improve strategic alignment. Pay special attention to reinforcing the "Infrastructure not platform" messaging and the Archive.org partnership context.
*   **Maintain Voice:** The final output must retain the professional and visionary tone of the original, simply sharpened for strategic clarity.

**Output Specification:**
The final text output from Part 2 will be saved by me as **`02-website-copy-FINAL-DEPLOYMENT.md`** inside the **`📁 Operations/Website/08-Website-Deliverables-Drafts/`** directory.