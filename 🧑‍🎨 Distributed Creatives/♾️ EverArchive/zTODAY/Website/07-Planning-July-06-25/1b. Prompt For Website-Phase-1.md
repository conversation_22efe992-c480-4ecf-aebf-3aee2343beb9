# Prompt for `1b. Prompt For Website-Phase-1.md`

You are an expert strategist and copywriter tasked with executing Phase 1 of the EverArchive Website Build Plan.

**Your Guiding Document:**
Your primary instruction set is the content of `📁 Operations/Website/07-Planning-July-06-25/1a. Website-Build-Plan-Phase-1-Strategy.md`.

**Your Source of Truth:**
Your sole source for all factual and conceptual information is the entire `📚 Canonical Library` directory structure and its contents.

**Your Mission is a Two-Part Process:**

**Part 1: Propose an Execution Plan (Planning Mode)**

Before writing any website copy, you must first analyze all provided source materials and create a detailed execution plan for my approval. This demonstrates you understand the task completely.

Your plan must include:
1.  A brief confirmation of the project's core strategic goals (e.g., "Foundations-First architecture," "Infrastructure, not platform," etc.).
2.  A section-by-section outline of the website content you intend to create.
3.  For **each** of the 7 sections (Hero, Foundations, Features, Software/Tools, Technology, About/Mission, Resources), you must specify:
    *   The key message or purpose of that section.
    *   The **specific documents from the `📚 Canonical Library`** you will use as the primary sources to generate the content for that section.

You will present this execution plan and then **STOP and WAIT** for my explicit approval. Do not proceed to Part 2 until I respond with "Plan approved, proceed with generation."

**Part 2: Generate the Final Content (Execution Mode)**

Upon my approval of your plan, you will execute it precisely.

**Your Final Deliverable:**
Produce a single, complete markdown file containing the full text for all 7 website sections. The content must be formatted clearly with Markdown headers for easy parsing and deployment.

**Execution Requirements (Constraints):**
*   **Adhere Strictly to the Strategy:** Follow the "Foundations-First" architecture and the "Infrastructure not Platform" positioning outlined in your guiding document.
*   **Use Correct Terminology:** Exclusively use "Deep Authorship Package (DAP)".
*   **Maintain a Cohesive Voice:** The tone must be authoritative, visionary, and professional, suitable for a conference audience of institutional partners.
*   **Structure for Deployment:** Use clear Markdown headers (`## Section: ...`) for each section.

**Output Specification:**
The final text output from Part 2 will be saved by me as **`01-website-copy-deliverable-phase-1.md`** inside the **`📁 Operations/Website/08-Website-Deliverables-Drafts/`** directory.