# Unified "Website Build Plan"

After running the prompt above, you will have two clean documents. Here is the new, overarching strategy that explains how to use them together. This can be created as a new `README.md` or a master plan document.

#### **The EverArchive Website Build Plan**

**Objective:** To execute a structured, two-phase process for generating, verifying, and deploying the EverArchive website, ensuring strategic alignment and content quality.

**Phase 1: Content Generation & Finalization**
*   **Guiding Document:** `Website-Build-Plan-Phase-1-Strategy.md`
*   **Purpose:** To define the website's structure, messaging, and content requirements. This document is the **source of truth for what the website should be.**
*   **Process:**
    1.  An AI agent (or team) uses this strategy document to perform deep research on the `📚 Canonical Library`.
    2.  The agent extracts and synthesizes all required content for the defined sections (Hero, Foundations, Features, etc.).
    3.  **The final deliverable of this phase is a single, version-controlled markdown file containing the complete website copy.** (e.g., `01-initial-launch-content.md`).

**Phase 2: Verification, Deployment & Archival Review**
*   **Guiding Document:** `Website-Build-Plan-Phase-2-Checklist.md`
*   **Purpose:** To act as a comprehensive "Quality Gate" before deployment. This document ensures that the content generated in Phase 1 **accurately reflects the strategy.**
*   **Process:**
    1.  **Step 2.1: Verification.** Use the checklist to audit the generated content file (`01-initial-launch-content.md`). Verify messaging alignment, content completeness, and strategic positioning (e.g., "infrastructure not platform").
    2.  **Step 2.2: Deployment.** Once verified, the content file is handed off to the deployment system (V0). Create the specific deployment plan for V0 to execute (see section 3 below).
    3.  **Step 2.3: Archival Plan Review.** **After a successful launch**, review the documents in the `archived-plans/` subdirectory. The goal is to identify any valuable "nuggets of wisdom," tactical ideas, or alternative framings that were missed. Create a short report of findings to inform the first post-launch iteration.

---

### 3. The Iterative Development & V0 Synchronization Strategy

This section addresses your crucial question about how to handle post-launch changes and keep the canonical project documents and the live website in sync.

#### The "Canonical-First" Iteration Workflow

The core principle is: **The EverArchive Canonical Library is the single source of truth.** The live website is a *deployed artifact* of that truth. **Changes are never made directly on the live site's backend.**

**The Loop:**

1.  **Identify Need for Change:** You see the live website and realize a sentence in the "Foundations" section needs to be clearer.

2.  **Update the Source of Truth:**
    *   **DO NOT** change the content in V0 or in the `01-initial-launch-content.md` file.
    *   Navigate to the relevant document in the `📚 Canonical Library` that is the ultimate source for that concept (e.g., `Tome I/1.2 - The Principles of Deep Authorship.md`).
    *   Make the change there. This ensures your core documentation is always up-to-date and authoritative.

3.  **Generate New Deployment Artifact:**
    *   Re-run a condensed version of "Phase 1" of the build plan. The goal is to generate a new, versioned markdown file that reflects the updated canonical information.
    *   This produces **`02-post-launch-update-content.md`**.

4.  **Create New V0 Deployment Plan:**
    *   You (or an AI) create a new, simple deployment plan for V0. It might look like this:
        ```markdown
        # V0 Deployment Plan v2
        1. Target file: `02-post-launch-update-content.md`
        2. Action: Update the content of the "Foundations" section on the live site with the content from `#section-foundations` in the target file.
        3. No other sections are affected.
        ```
    *   This plan is given to V0 to execute.

This workflow guarantees that your canonical documentation and your live website never drift apart. The website is always a reflection of the core project truth.

---

### 4. The Content Versioning System

As you requested, here is the formal file-naming convention for the website content artifacts generated by this process.

*   **Convention:** `[version-number]-[short-description].md`
*   The `[version-number]` will be a two-digit number, zero-padded (e.g., `01`, `02`, `03`).
*   The `[short-description]` should be a brief, kebab-case summary of the version's purpose.

**Examples:**

*   `01-initial-conference-launch.md`
*   `02-post-conference-messaging-updates.md`
*   `03-institutional-partner-features-added.md`

This system provides a clear, chronological history of the deployed website content, making it easy to track changes and, if necessary, roll back to a previous version.