# EverArchive Website Structure v1.0
**Created:** 2025-06-22  
**Purpose:** Raw website structure for immediate launch  
**Timeline:** Ready for development in next 2-3 days

---

## Site Architecture

### Core Pages (Launch Critical)

```
/
├── index.html (Landing Page)
├── whitepaper.html (White Paper page with download)
├── about.html (Deep Authorship philosophy)
├── how-it-works.html (3-layer model explanation)
├── join.html (Email capture & community)
└── faq.html (Key questions answered)
```

### Phase 2 Pages (Post-Launch)

```
├── for-creators.html (Creator benefits)
├── for-institutions.html (Partnership info)
├── technology.html (Technical details)
├── blog/ (Updates and thoughts)
└── docs/ (Technical documentation)
```

---

## Page Content Specifications

### 1. Landing Page (index.html)
**Source:** `/z - launch strategy/landing-page-L3-copy.md`
- Hero section with main value prop
- Problem/Solution narrative
- How it works (visual)
- Email capture form
- Links to White Paper and FAQ

### 2. White Paper Page (whitepaper.html)
**Source:** `/📚 Canonical Library/Whitepaper/Whitepaper - Draft 1.md`
- Executive summary (pulled from white paper)
- Download button for full PDF
- Key insights preview
- Email capture for updates

### 3. About Page (about.html)
**Source:** `/📚 Canonical Library/Tome I - The Vision/1.2 - The Principles of Deep Authorship.md`
- Deep Authorship philosophy
- The twin crises
- Our mission
- Core principles (simplified)

### 4. How It Works (how-it-works.html)
**Source:** Technical but simplified from `.dao` spec
- Visual representation of 3 layers
- Simple workflow diagram
- Security/sovereignty explanation
- Next steps CTA

### 5. Join Page (join.html)
**Source:** New content needed
- Email newsletter signup
- Community links (Discord/GitHub)
- Early access interest form
- "Spread the word" social tools

### 6. FAQ Page (faq.html)
**Source:** `/📚 Canonical Library/Documentation/04 - Stakeholder FAQ.md`
- Creator questions
- Technical questions
- Institutional questions
- Philosophy questions

---

## Design Requirements

### Visual Identity (Minimal v1)
- **Typography:** Clean, readable, timeless
- **Colors:** Minimal palette (black, white, one accent)
- **Logo:** Simple wordmark or symbol
- **Images:** Diagrams for 3-layer model

### Technical Requirements
- **Framework:** Static HTML/CSS/JS (no complex build)
- **Responsive:** Mobile-first design
- **Performance:** < 3 second load time
- **Accessibility:** WCAG 2.1 AA compliant
- **Analytics:** Privacy-respecting (Plausible/Fathom)

### Interactive Elements
- Email capture forms
- White paper download
- Smooth scroll navigation
- Optional: Interactive 3-layer diagram

---

## Content Management

### Immediate Needs
1. Convert markdown to HTML
2. Create visual for 3-layer model
3. Set up email capture service
4. Generate PDF of white paper
5. Write join page content

### Launch Checklist
- [ ] Domain secured (everarchive.org?)
- [ ] Hosting setup (Netlify/Vercel/GitHub Pages)
- [ ] Email service connected
- [ ] Analytics configured
- [ ] SSL certificate active
- [ ] Basic SEO meta tags
- [ ] Social sharing images

---

## Phased Rollout (from launch strategy.md)

### Day 1: Scaffold
- Basic HTML structure live
- Domain pointing correctly
- Email capture working

### Day 2: Core Content
- Landing page complete
- White paper downloadable
- About and FAQ pages live

### Week 1: Full v1
- All 6 core pages complete
- Visual design applied
- Mobile responsive
- Analytics tracking

### Week 2: Polish
- Design refinements
- Performance optimization
- SEO improvements
- Social sharing setup

---

## Key Messages to Emphasize

1. **Not just another backup solution**
2. **Creator sovereignty is paramount**
3. **Preserving process, not just products**
4. **Non-profit, public good infrastructure**
5. **Built for centuries, not quarters**

---

## Technical Implementation Notes

### For Developers
- Start with semantic HTML5
- Use CSS Grid/Flexbox for layout
- Implement Progressive Enhancement
- Keep JavaScript minimal and optional
- Ensure works without JS enabled

### Content Security
- CSP headers for security
- No external trackers
- Self-host all assets
- GDPR compliant email capture

---

## Success Metrics

### Launch Week
- Site live and stable
- 100+ email signups
- White paper downloads
- No critical bugs

### First Month  
- 1,000+ unique visitors
- 500+ email subscribers
- Press/blog coverage
- Partnership inquiries

---

## Next Steps

1. **Content Prep** - Convert all markdown to web-ready HTML
2. **Design Sprint** - Create minimal visual system
3. **Development** - Build static site
4. **Testing** - Cross-browser and device testing
5. **Launch** - Deploy and announce

**Remember:** Perfect is the enemy of good. Launch simple, iterate based on feedback.