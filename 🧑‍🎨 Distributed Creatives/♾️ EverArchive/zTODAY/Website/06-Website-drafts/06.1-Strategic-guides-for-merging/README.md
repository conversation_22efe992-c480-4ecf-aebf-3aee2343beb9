# STRATEGIC GUIDES FOR WEBSITE MERGING
**Purpose**: Definitive rules and guidelines for AI agents merging website drafts  
**Authority**: These documents define the strategic foundation and non-negotiable principles  
**Use**: Reference these guides to ensure merged content aligns with EverArchive strategy  

---

## 📋 **STRA<PERSON><PERSON><PERSON> GUIDE DOCUMENTS**

### **COMPREHENSIVE-WEBSITE-STRATEGY-FINAL.md**
- **Purpose**: Complete strategic foundation for website approach
- **Authority**: DEFINITIVE strategic positioning
- **Contains**: 
  - Multi-stakeholder approach
  - Infrastructure partnership positioning
  - Audience hierarchy and messaging priorities
  - Content architecture principles
- **Use**: Primary strategic reference for all merging decisions

### **MASTER-WEBSITE-PLAN-2025-07-05.md**
- **Purpose**: Current deployment plan and status overview
- **Authority**: Current strategic state and deployment requirements
- **Contains**:
  - Deployment timeline and requirements
  - Strategic positioning decisions
  - Quality standards and completion criteria
- **Use**: Deployment requirements and current strategic direction

### **FOUNDATIONAL-CONCEPTS-EXTRACTED.md**
- **Purpose**: Core EverArchive concepts and terminology
- **Authority**: DEFINITIVE concept definitions
- **Contains**:
  - Deep Authorship Package (DAP) definitions
  - 3-Layer Model specifications
  - Infrastructure partnership concepts
  - Technical terminology standards
- **Use**: Ensure consistent terminology and concept usage

---

## 🎯 **NON-NEGOTIABLE STRATEGIC PRINCIPLES**

### **1. TERMINOLOGY STANDARDS**
- ✅ **REQUIRED**: "Deep Authorship Package (DAP)"
- ❌ **FORBIDDEN**: ".dao format" (outdated)
- ✅ **REQUIRED**: "Infrastructure partnership" approach
- ❌ **FORBIDDEN**: Positioning as storage provider replacement

### **2. POSITIONING HIERARCHY**
- **PRIMARY**: Infrastructure for institutions (libraries, archives, researchers)
- **SECONDARY**: Creator sovereignty and benefits
- **TERTIARY**: General public appeal
- **NEVER**: Pure blockchain/crypto positioning

### **3. CONTENT ARCHITECTURE** (Foundations-First)
```
Hero → Foundations → Features → Software/Tools → Technology → About/Mission → Resources
```

### **4. MESSAGING PRIORITIES**
1. **Infrastructure Partnership**: We coordinate WITH storage providers
2. **Institutional Compatibility**: Library systems, archival standards
3. **Complete Process Preservation**: Not just final works, but creative journey
4. **Creator Sovereignty**: Progressive control with zero-knowledge encryption
5. **Civilizational Memory**: Long-term cultural preservation mission

---

## 📊 **AUDIENCE-SPECIFIC MESSAGING RULES**

### **FOR INSTITUTIONS** (Primary):
- Emphasize compatibility with existing systems
- Highlight archival standards compliance
- Focus on infrastructure coordination
- Professional, academic tone

### **FOR CREATORS** (Secondary):
- Emphasize sovereignty and control
- Highlight creative process preservation
- Focus on legacy and permanence
- Accessible but not overly emotional

### **FOR DEVELOPERS** (Tertiary):
- Emphasize technical capabilities
- Highlight open standards and APIs
- Focus on integration possibilities
- Technical but not overwhelming

---

## 🔧 **CONTENT QUALITY STANDARDS**

### **PROFESSIONAL REQUIREMENTS**:
- Conference-presentation ready
- Academic/institutional appropriate
- Technical accuracy verified
- No marketing hyperbole

### **COMPLETENESS REQUIREMENTS**:
- All 7 sections fully developed
- Each section serves strategic purpose
- No placeholder or incomplete content
- Clear progression from concept to action

### **LENGTH STANDARDS**:
- **Target**: 375-400 lines total
- **Minimum**: 350 lines (ensure completeness)
- **Maximum**: 450 lines (avoid overwhelming)

---

## 🎯 **MERGING DECISION FRAMEWORK**

### **WHEN CONTENT CONFLICTS**:
1. **Check strategic guides** - Does it align with core principles?
2. **Prioritize audience** - Does it serve institutional audience first?
3. **Verify terminology** - Does it use current DAP terminology?
4. **Assess completeness** - Does it contribute to section completeness?
5. **Evaluate professionalism** - Is it conference-appropriate?

### **CONTENT SELECTION PRIORITY**:
1. **Strategic alignment** (most important)
2. **Institutional appeal** 
3. **Professional quality**
4. **Completeness contribution**
5. **Creator appeal** (secondary)

---

## 📝 **AI AGENT INSTRUCTIONS**

### **BEFORE MERGING**:
1. **Read all strategic guides** thoroughly
2. **Understand positioning hierarchy** (infrastructure-first)
3. **Memorize terminology standards** (DAP not .dao)
4. **Internalize content architecture** (foundations-first)

### **DURING MERGING**:
1. **Reference guides constantly** for decision-making
2. **Prioritize strategic alignment** over creative preference
3. **Maintain professional tone** throughout
4. **Ensure section completeness** before moving on

### **AFTER MERGING**:
1. **Validate against all guides** for compliance
2. **Check terminology consistency** throughout
3. **Verify professional quality** standards
4. **Confirm strategic positioning** is clear

---

## ✅ **VALIDATION CHECKLIST**

Before finalizing merged content, ensure:
- ✅ DAP terminology used consistently (not .dao)
- ✅ Infrastructure partnership clearly explained
- ✅ Institutional audience prioritized
- ✅ All 7 sections complete and substantial
- ✅ Professional conference-ready tone
- ✅ 375-400 lines total length
- ✅ Strategic positioning clear throughout
- ✅ Creator benefits included but secondary

**These guides are AUTHORITATIVE - follow them strictly for successful merging!**
