# Website Drafts - Clean Organization

## Directory Structure

### 06.1-Strategic-guides-for-merging/
Planning documents, strategy guides, and development plans for website creation.

### 06.2-Website-drafts-all-versions/
All website content versions in chronological order. Each file is dated and named for easy comparison.

## File Naming Convention
- Format: `YYYY-MM-DD-HHMM-Descriptive-name-with-dashes.md`
- Chronological ordering for easy comparison
- Clear descriptive names indicating content focus

## Usage
1. Review files in 06.2 chronologically to understand evolution
2. Use 06.1 for strategic context and planning background
3. Select best elements from multiple versions for final deployment

## Status
✅ Consolidated from 5+ scattered directories
✅ Removed duplicates and empty directories
✅ Clean chronological organization
✅ Ready for content review and merging decisions