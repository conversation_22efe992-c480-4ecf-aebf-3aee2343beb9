# AI AGENT MANDATORY BRIEFING
## Required Reading for All AI Assistants Working on EverArchive

### CRITICAL REQUIREMENT
Before making ANY changes to the EverArchive project, you MUST:
1. Read this entire briefing
2. Check the EVERARCHIVE-CHANGE-LOG.md for recent modifications
3. Understand the infrastructure vs platform distinction
4. Document all changes in the change log

### PROJECT IDENTITY
**EverArchive is**:
- Non-profit infrastructure for preserving human creative process
- A refusal to forget who we are as a species
- Zero-knowledge encryption ensuring creator sovereignty
- The roads, not the vehicles

**EverArchive is NOT**:
- A platform for monetization
- A marketplace for creative works
- A payment processor
- A revenue generator

### CONTAMINATION HISTORY
Previous AI assistants have accidentally introduced commercial/platform language that contradicts the core mission. This contamination originated from:
1. Multi-persona exercises introducing "CFO perspectives"
2. Benefits brainstorming defaulting to monetization
3. Well-meaning attempts to ensure "sustainability" through revenue

### MANDATORY LANGUAGE RULES

**FORBIDDEN WORDS/CONCEPTS**:
- Monetize/Monetization
- Marketplace
- Revenue models
- Customer/Consumer
- Platform (when referring to EverArchive)
- Earnings through EverArchive
- Value capture
- Pricing/Commission

**REQUIRED FRAMING**:
- Infrastructure that enables
- Non-profit that doesn't touch money
- Preservation, not monetization
- Creator sovereignty
- Value enablement (not capture)

### REFERENCE HIERARCHY
When working on EverArchive, reference ONLY these documents in order:
1. `/📁 Operations/Vision Preservation/SACRED-VISION-DOCUMENT-EverArchive.md`
2. `/📚 Canonical Library/Tome I - The Vision/` (corrected versions)
3. Original October 2024 Forever Sites documents
4. Technical specifications

**NEVER reference**:
- Documents in quarantine directories
- Benefits analyses with monetization focus
- Multi-persona debate transcripts
- Any document suggesting revenue models

### CHANGE DOCUMENTATION PROTOCOL
For EVERY change you make:
1. Run `date '+%Y-%m-%d %H:%M:%S %Z'` to get current timestamp
2. Add entry to `/♾️ EverArchive/EVERARCHIVE-CHANGE-LOG.md`
3. Include:
   - Timestamp
   - Your agent identifier
   - Files affected
   - Clear description
   - Rationale for change
   - Review status

### VISION PROTECTION CHECKLIST
Before submitting any content, verify:
- [ ] No monetization language introduced
- [ ] Infrastructure framing maintained
- [ ] Creator sovereignty respected
- [ ] No platform features suggested
- [ ] Change log updated
- [ ] References only approved documents

### CONTAMINATION RESPONSE
If you identify commercial/platform language:
1. Do NOT propagate it to new documents
2. Flag it for quarantine
3. Create corrected version
4. Document in change log
5. Reference SACRED-VISION-DOCUMENT for proper language

### CORE MISSION REMINDER
EverArchive exists because someone watched their friend's creative legacy face extinction. It's about grief, memory, and the refusal to let human creativity be erased or homogenized. Every line of code, every document, every feature must serve this mission of preservation, not profit.

---

**By proceeding with any EverArchive work, you acknowledge reading and understanding these requirements.**

*Last Updated: 2025-07-01 by Documentation Analyst*