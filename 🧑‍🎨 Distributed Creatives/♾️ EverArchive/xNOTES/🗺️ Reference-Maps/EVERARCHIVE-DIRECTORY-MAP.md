# EverArchive Complete Directory Structure Map

Generated: July 1, 2025

## Overview

The EverArchive project is organized into a comprehensive Obsidian vault structure with clear separation between canonical documentation, active work, publications, operations, and archived content.

## Root Directory Structure

```
♾️ EverArchive/
├── 📚 Canonical Library/        [101 files] - Source of truth for all documentation
├── 📋 Active Work/              [310 files] - Current development and research
├── 📖 Publications/             [70 files]  - External-facing materials
├── 📁 Operations/               [39 files]  - Project management and AI collaboration
├── 📦 Archive/                  [149 files] - Historical reference (READ-ONLY)
├── docs/                        [3 files]   - Changelogs and handovers
└── [Root documentation files]   [10 files]  - Project indices and briefings
```

Total: 682 markdown files across the project

## 📚 Canonical Library (Source of Truth)

The Canonical Library contains 101 files organized into 4 Tomes plus supporting documentation:

### Documentation/ [23 files]
- Core documentation standards and processes
- **Key Files:**
  - `00 - Canonical Library - Master Index.md`
  - `01 - Weighted Insights.md` (critical user research)
  - `02 - Master Strategy & Roadmap.md`
  - `04 - Stakeholder FAQ.md`

### Tome I - The Vision/ [2 files]
- Foundational vision documents
- **Key Files:**
  - `1.1 - The EverArchive Manifesto.md`
  - `1.2 - The Principles of Deep Authorship.md`

### Tome II - The Architecture/ [61 files]
- Technical specifications and user journeys
- **Structure:**
  - `2.1 - Technical Architecture.md`
  - `2.2 - Deep Authorship Object Technical Specification.md`
  - `2.3 - Key Management Architecture.md`
  - `2.4 - Integration Specifications.md`
  - `2.5 - User Journeys & Lifecycle Models/`
    - `1. Creator Journeys/` (16 detailed journey maps)
    - `2. Legacy and Institutional Journeys/` (11 journey maps)
    - `3. Academic & Institutional Knowledge Journeys/` (8 journey maps)

### Tome III - The Operations/ [9 files]
- Governance and operational frameworks
- **Key Files:**
  - `3.1 - Governance Framework.md`
  - `3.2 - Economic Model.md`
  - `3.3 - Partnership Strategy.md`
  - `3.4 - Community Guidelines.md`
  - `3.5 - Operational Procedures.md`

### Tome IV - The Implementation/ [5 files]
- Roadmaps and deployment strategies
- **Key Files:**
  - `4.1 - Product Roadmap.md`
  - `4.2 - MVP Requirements.md`
  - `4.3 - Pilot Program Design.md`
  - `4.4 - Go-to-Market Strategy.md`
  - `4.5 - Success Metrics.md`

### Whitepaper/ [1 file]
- `Whitepaper - Draft 1.md`

## 📋 Active Work (310 files)

Current development organized by research rounds:

### Round 1 - Phase 1 Research Integration/
- Comprehensive research across 15 priority areas
- **Research/Research 1/** - Initial feasibility studies
- **Research/Research 2/** - Deep technical research
- **Research/Research 3/** - Market and institutional analysis

### Round 2 - Website Launch Pivot/
- Strategic pivot documentation
- Website content development
- Launch planning materials

## 📖 Publications (70 files)

External-facing materials and formal publications:

### Conference Materials/
- Presentation decks
- Speaking notes
- Workshop materials

### Website Content/
- Landing page copy
- Feature descriptions
- User guides

### Whitepaper/v3-working/
- **final/** - Production-ready whitepaper materials
- **content-drafts/** - Section drafts
- **research/** - Supporting research
- **qa-outputs/** - Quality assurance results
- **synthesis-work/** - Integration documents

## 📁 Operations (39 files)

Project management and collaboration infrastructure:

### AI Collaboration/
- `EVERARCHIVE-PROJECT-CONTEXT.md` - Master AI briefing
- `Agent Reports/Phase One/` - Systematic AI research reports
- Research prompts and protocols

### Project History/
- Timeline documentation
- Decision logs
- Migration records

### Research Coordination/
- Research protocols
- Synthesis frameworks
- Quality standards

### Team Handovers/
- Systematic handover documentation
- Next agent briefings
- Context preservation

### Vision Preservation/
- Core vision documentation
- Strategic alignment guides
- Mission continuity

## 📦 Archive (149 files) - HISTORICAL REFERENCE ONLY

Contains superseded content organized by migration date:

### Major Archive Sections:
- `2025-06-22-consolidation/` - First major consolidation
- `2025-cleanup/` - Recent cleanup efforts
- `Centering the Archive/` - Alternative organization (EA-prefixed)
- `EverArchive (was Forever Sites)/` - Original project materials
- `Legacy Forever Sites (Oct 2024)/` - Pre-rebrand content
- `Research/` - Original research rounds
- `🚫 CONTAMINATED-COMMERCIAL/` - Materials with licensing issues

## docs/ (3 files)

New standardized location for:
- `changelogs/` - Systematic change documentation
- `handovers/` - AI agent transition documents

## Root Documentation Files (10 files)

Critical project-level documentation:
- `00 - PROJECT MASTER INDEX.md` - Complete project navigation
- `AI-AGENT-MANDATORY-BRIEFING.md` - Required AI context
- `CLAUDE.md` - Claude Code specific instructions
- `README.md` - Standard project documentation
- `EVERARCHIVE-CHANGE-LOG.md` - High-level changes
- Additional extraction and reference documents

## Navigation Guidelines

1. **Always start with Canonical Library** for authoritative information
2. **Check Active Work** for current development
3. **Use Publications** for external communications
4. **Reference Operations** for process and collaboration
5. **Archive is READ-ONLY** - contains historical versions

## File Organization Standards

- **Numbered prefixes** (e.g., "01 -") indicate reading order
- **Emoji prefixes** indicate directory purpose
- **Tome structure** follows academic organization
- **User journeys** numbered sequentially (2.5.1, 2.5.2, etc.)
- **Research rounds** indicate iterative development

## Key Entry Points

1. **New to project**: Start with `/README.md`
2. **AI agents**: Read `/AI-AGENT-MANDATORY-BRIEFING.md`
3. **Documentation**: Begin at `/📚 Canonical Library/Documentation/00 - Canonical Library - Master Index.md`
4. **User insights**: `/📚 Canonical Library/Documentation/01 - Weighted Insights.md`
5. **Technical specs**: `/📚 Canonical Library/Tome II - The Architecture/`
6. **Current work**: `/📋 Active Work/Round 2 - Website Launch Pivot/`