# EverArchive Documentation Map
*Generated: 2025-07-01*

## Overview Statistics
- **Total Markdown Files**: 690
- **Primary Directories**: 5 major organizational areas
- **Large Files (>50KB)**: 47 documents
- **Empty/Stub Files**: 18 documents

## Directory Structure & File Counts

### 📚 Canonical Library (102 files) - AUTHORITATIVE VERSIONS
The official, consolidated documentation organized into 4 Tomes + supporting docs.

**Breakdown:**
- **Documentation/** (23 files) - Meta-documentation, workflows, AI prompts
- **Tome I - The Vision/** (2 files) - Core vision documents
  - The EverArchive Manifesto
  - The Principles of Deep Authorship
- **Tome II - The Architecture/** (61 files) - Technical specifications
  - Canonical Architecture
  - Deep Authorship Object Technical Specification
  - User Journeys (42 individual journey documents)
  - Discovery & Access Infrastructure
  - Creator Tools Framework
- **Tome III - The Operations/** (9 files) - Governance and operations
  - Governance Constitution
  - Economic Framework
  - Partnership Protocols
  - Community Stewardship
  - Resilience & Recovery Plan
  - Technical Evolution Framework
  - Cultural Translation Guide
- **Tome IV - The Implementation/** (5 files) - Execution documents
  - Project & Product Roadmap
  - Communications & Fundraising Kit
  - Research & Gap Analysis Dossier
  - Market Analysis & Positioning
  - Financial Projections & Models
- **Whitepaper/** (1 file) - Draft 1 only

### 📋 Active Work (310 files) - WORK IN PROGRESS
Current development efforts organized by rounds.

**Breakdown:**
- **Round 1 - Phase 1 Research Integration/** (285 files)
  - Extensive research outputs across 3 research subdirectories
  - Research on decentralized storage, universities, Internet Archive
  - DAO format analysis
  - Integration guides and workflows
- **Round 2 - Website Launch Pivot/** (24 files)
  - Strategic format pivot documentation
  - Website content development (8 page templates)
  - Launch strategy documents
  - Documentation conflict resolution

### 📁 Operations (45 files) - PROJECT MANAGEMENT
Operational documentation, AI collaboration protocols, and team handovers.

**Key Sections:**
- **AI Collaboration/** (11 files)
  - Agent reports and prompts
  - Project context documents
  - Phase One documentation review outputs
- **Project History/** (6 files)
  - Audit reports and comparisons
  - Update summaries
- **Research Coordination/** (5 files)
  - Master research inventory
  - Research prompt catalogs
- **Team Handovers/** (15 files)
  - Multiple handover documents for AI agent continuity
  - Briefing documents
  - Vision preservation materials
- **Vision Preservation/** (11 files)
  - Monetization language audit
  - Sacred vision documents
  - Strategic recommendations

### 📖 Publications (70 files) - OUTPUT DOCUMENTS
Materials prepared for external consumption.

**Structure:**
- **Conference Materials/** (empty directory)
- **Website Content/** (2 files)
  - Conference prep
  - Website blueprint
- **Whitepaper/v3-working/** (67 files)
  - Complete whitepaper development system
  - Source materials (canonical docs)
  - Content drafts (8 sections)
  - Research responses
  - Discovery outputs
  - Strategy outputs
  - Final generation scripts and templates

### 📦 Archive (149 files) - HISTORICAL VERSIONS
Previous iterations, migrations, and superseded content.

**Major Sections:**
- **2025-06-22-consolidation/** (52 files)
  - Previous consolidation attempt
  - Multiple architecture versions
  - Manifesto versions
  - "Centering the Archive" alternative organization
- **Legacy Forever Sites (Oct 2024)/** (8 files)
  - Original project documentation
  - Notion export from October 2024
- **Research/** (45 files in Round 1, 6 in Round 2, 1 in Round 3)
  - Historical research rounds
  - Decentralized storage analysis
- **EverArchive (was Forever Sites)/** (7 files)
  - Transformation documentation
  - Master synthesis documents
- **💬⚒ Conversation Processing/** (7 files)
  - Synthesis directives and workflows
- **🚫 CONTAMINATED-COMMERCIAL/** (29 files)
  - Quarantined commercial-focused content
  - Multi-persona debates
  - Benefits brainstorming
  - Website copy with commercial bias

## Key Document Patterns & Duplications

### Deep Authorship Manifesto Versions (6+ versions found)
1. Canonical: `📚 Canonical Library/Tome I - The Vision/1.1 - The EverArchive Manifesto.md`
2. Archive versions in multiple locations
3. Synthesis editions in Centering the Archive
4. Legacy versions from Forever Sites

### Technical Architecture Documents (12+ versions)
1. Canonical: `📚 Canonical Library/Tome II - The Architecture/2.1 - Canonical Architecture.md`
2. Multiple synthesis editions in Archive
3. Whitepaper source version
4. Research outputs with architecture analysis

### DAO Format Specifications (6 versions)
1. Canonical: `📚 Canonical Library/Tome II - The Architecture/2.2 - Deep Authorship Object Technical Specification.md`
2. Summary version: `2.2a - DAO Format Summary for Research.md`
3. Various analysis and comparison documents

### User Journey Documents (53 total)
- 42 in Canonical Library (organized by creator type and legacy/institutional)
- Additional drafts and templates
- Extraction and analysis documents

## Document Categories by Type

### Vision & Philosophy
- Manifestos (6+ versions)
- Deep Authorship principles
- Sacred vision documents
- Vision preservation materials

### Technical Documentation
- Architecture specifications (12+ versions)
- DAO format specs (6 versions)
- Discovery infrastructure
- Creator tools framework
- Technical evolution framework

### Research Materials
- **Round 1**: 285 files (extensive research integration)
- **Round 2**: 6 files (focused research)
- **Round 3**: 1 prompt document
- Research coordination documents
- Master research inventory

### Governance & Operations
- Governance constitution
- Economic framework
- Partnership protocols
- Community stewardship
- Resilience planning
- Cultural translation

### User Experience
- 53 user journey documents
- Creator journeys (16 types)
- Legacy/institutional journeys (21 types)
- Academic journeys (5 types)

### Project Management
- Handover documents (15+)
- AI collaboration protocols (11)
- Audit reports
- Timeline tracking
- Change logs

## Organizational Observations

### Strengths
1. **Canonical Library structure** provides clear authoritative versions
2. **Tome organization** creates logical groupings
3. **Extensive user journey coverage** (42 detailed scenarios)
4. **Rich research documentation** with systematic organization
5. **Clear quarantine** of contaminated commercial content

### Challenges
1. **Multiple document versions** scattered across directories
2. **Active Work contains 45% of all files** - needs consolidation
3. **Whitepaper exists only as Draft 1** in Canonical Library
4. **Empty stub files** in research directories
5. **Parallel organizational systems** (Centering the Archive vs Canonical Library)

### Consolidation Opportunities
1. **Manifesto versions** - 6+ versions need reconciliation
2. **Architecture documents** - 12+ versions across directories
3. **Research materials** - 3 rounds in different locations
4. **Handover documents** - 15+ files with overlapping content
5. **Website content** - Split between Active Work and Publications

## Recommended Actions
1. Complete whitepaper beyond Draft 1 using v3-working materials
2. Consolidate duplicate manifesto and architecture versions
3. Archive completed Active Work items
4. Fill in empty research stub files or remove them
5. Create master index linking all user journeys
6. Consolidate handover documents into single briefing
7. Complete Conference Materials directory
8. Migrate successful Active Work to appropriate Canonical sections