# EverArchive Order of Operations & Dependencies

**Created**: 2025-07-02
**Purpose**: Master sequencing document for all EverArchive work streams
**Status**: Strategic planning document

---

## Executive Summary

This document establishes the logical order of operations for EverArchive development, ensuring work proceeds efficiently without conflicts or rework. Work is organized by dependency chains and critical paths.

## 🚨 IMMEDIATE PRIORITIES (Day 1-2)

### 1. Quarantine Contaminated Website Content (30 min) - CRITICAL BLOCKER
**Why First**: Prevents accidental use of monetization language
**Dependencies**: None - must happen before ANY content work
**Location**: `/📋 Active Work/Round 2/07 - Website Content Development/`
**Actions**:
- [ ] Create `/📋 Active Work/QUARANTINE/` directory
- [ ] Move entire `07 - Website Content Development/` folder there
- [ ] Add WARNING-CONTAMINATED.md explaining monetization issues
- [ ] Alert team not to use quarantined content

### 2. Commercial Language Quick Wins (1 hour) - BLOCKER
**Why Second**: Legal/regulatory risk to non-profit status
**Dependencies**: Contaminated content quarantined
**Location**: `/📋 Active Work/Round 2/Quick-Wins-Immediate-Changes.md`
**Actions**:
- [ ] Backup current website
- [ ] Execute 15 immediate changes
- [ ] Verify no "monetization" on homepage
- [ ] Delete earnings percentage claims
- [ ] Update CTAs

### 3. Operational Documentation Review (1 hour)
**Why Third**: Can proceed while website fixes happen
**Dependencies**: None - research complete
**Location**: `/📋 Active Work/Operational Documentation Sprint/`
**Actions**:
- [ ] Review 4 document prompts
- [ ] Refine as needed
- [ ] Prepare for execution

### 4. Canonical Library Reorganization (1 hour) - FOUNDATION
**Why Early**: Establishes single source of truth before content work
**Dependencies**: None - can run parallel
**Location**: Various (see details below)
**Actions**:
- [ ] Move 4 analysis docs from whitepaper to Canonical Library
- [ ] Clean whitepaper directory of duplicates
- [ ] Create SHARING-GUIDE.md for AI collaboration
- [ ] Verify Canonical Library is complete

## 🔥 CRITICAL PATH (Week 1)

### 5. Commercial Language Phase 1&2 (5 hours)
**Why Now**: Must complete before any website content work
**Dependencies**: Quick wins complete, contaminated content quarantined
**Location**: `/📋 Active Work/Round 2/Change-Priority-Matrix.md`
**Actions**:
- [ ] Execute priority matrix phases 1 & 2
- [ ] Rewrite commercial sections
- [ ] Update all navigation

### 6. Operational Documentation Creation (6-8 hours)
**Why Parallel**: Can run alongside language fixes
**Dependencies**: Prompt review complete
**Location**: Canonical Library integration
**Actions**:
- [ ] Execute 4 document prompts
- [ ] Quality assurance
- [ ] Integrate into Tome III
- [ ] Create placeholder docs

### 7. Website Blueprint Review (2 hours)
**Why Next**: Must understand structure before content work
**Dependencies**: Commercial language fixes complete, Canonical Library reorganized
**Location**: `/📖 Publications/Website Content/website-blueprint.md`
**Actions**:
- [ ] Study site architecture
- [ ] Map content sources to Canonical Library ONLY
- [ ] Verify all source docs current and clean

## 📅 WEEK 1-2 IMPLEMENTATION

### 6. Benefits Framework Transformation (Week 2)
**Why Before Content**: New benefits must inform all content
**Dependencies**: Commercial language alignment
**Location**: `/📋 Active Work/Round 2/Benefits-Audit-Analysis.md`
**Actions**:
- [ ] Transform 47+ benefits
- [ ] Create new hierarchy
- [ ] Document approved patterns

### 6a. Strategic Messaging Guide (Week 2 - 2 hours)
**Why Now**: Captures valuable early insights before content work
**Dependencies**: Benefits framework for consistency
**Location**: Create in Tome IV
**Source**: `/📋 Active Work/Round 1/Additional Ideas/`
**Actions**:
- [ ] Extract phrases from vision.md and Chat Thoughts
- [ ] Document "process as meditation" concept
- [ ] Capture "rights-first memory system" language
- [ ] Create approved phrase library

### 6b. Philosophical Vision Updates (Week 2 - 2 hours)
**Why Now**: Strengthens foundation before website content
**Dependencies**: Strategic messaging guide
**Location**: Tome I - The Vision documents
**Actions**:
- [ ] Add creative process as self-actualization to manifesto
- [ ] Integrate "channeling quality" concept
- [ ] Strengthen "authorship over machine-readable reality"
- [ ] Update vision documents with deeper framing

### 7. Website Content Development (Week 1-2)
**Why Now**: Foundation work complete
**Dependencies**: 
- Benefits framework done
- Commercial language clean
- Blueprint reviewed
**Location**: Multiple sources per blueprint
**Actions**:
- [ ] Extract from Canonical Library
- [ ] Apply new messaging
- [ ] Build static structure
- [ ] Implement interactive elements

### 8. Technical Pipeline Setup (2 days)
**Why Parallel**: Can setup while content develops
**Dependencies**: None technical
**Location**: `/📖 Publications/Whitepaper/v3-working/final/`
**Actions**:
- [ ] Configure Pandoc pipeline
- [ ] Test HTML generation
- [ ] Setup deployment

## 📆 MONTH 1 COMPLETION

### 9. Messaging Guide Creation (4 hours)
**Why After Content**: Document what worked
**Dependencies**: All content complete
**Actions**:
- [ ] Document language patterns
- [ ] Create review checklist
- [ ] Before/after examples

### 10. Research Integration Updates (Month 1 - 2 hours)
**Why Now**: Captures missing research before archival
**Dependencies**: None - can run parallel
**Location**: Research & Gap Analysis Dossier
**Source**: COMPLETENESS AUDIT RESULTS
**Actions**:
- [ ] Add Institutional Repository Integration Patterns
- [ ] Add Preservation Quality Metrics Framework
- [ ] Add Embodied Knowledge Capture Systems
- [ ] Add Proof-of-Creativity Protocol
- [ ] Update research priorities

### 11. Technical Proposals Evaluation (Month 1)
**Why Later**: Not blocking critical path
**Dependencies**: Core architecture stable
**Actions**:
- [ ] Chia Research (RES-P2-04) - 4-6 hours
- [ ] EverCapsule Specification (if relevant) - 3 hours
- [ ] Integration decisions with technical team

### 12. Round 2 & Round 1 Integration
**Why Last**: Cleanup and archival
**Dependencies**: All active work complete
**Location**: `/📋 Active Work/`
**Actions**:
- [ ] Move Round 2 materials to Canonical Library
- [ ] Integrate Round 1 valuable concepts
- [ ] Archive working documents
- [ ] Update master indexes

## 🔄 ONGOING/PARALLEL WORK

### Research Repositioning (Throughout Month 1)
**Can Run Parallel**: Not blocking other work
**Actions**:
- [ ] Audit Phase 1 for EAD3 bias
- [ ] Update to preliminary findings
- [ ] Plan Phase 2 research

### Cross-Project Integration (After Week 2)
**Dependencies**: Core website complete
**Actions**:
- [ ] DC modal integration
- [ ] Shared authentication
- [ ] Design language alignment

## ⚠️ CRITICAL DEPENDENCIES

### Blocking Dependencies
1. **Contaminated content MUST be quarantined before**:
   - Any website content work
   - Any reference to Active Work files
   - Team members accessing old content

2. **Commercial language MUST be fixed before**:
   - Any new content creation
   - Website deployment
   - Institutional outreach

3. **Canonical Library reorganization MUST be complete before**:
   - Whitepaper rebuild
   - AI collaboration sharing
   - Content extraction for website

4. **Benefits framework MUST be complete before**:
   - Final website content
   - Marketing materials
   - Grant applications

5. **Website blueprint MUST be reviewed before**:
   - Content extraction begins
   - Technical implementation

6. **Strategic messaging guide SHOULD be complete before**:
   - Final website content polish
   - External communications
   - But NOT blocking initial content development

### Non-Blocking Parallel Tracks
- Operational documentation (can run anytime)
- Technical pipeline setup (independent)
- Research repositioning (ongoing)

## 📊 Success Metrics & Gates

### Day 1-2 Gate
- [ ] Zero monetization language on website
- [ ] Operational doc prompts refined
- [ ] No legal/regulatory exposure

### Week 1 Gate
- [ ] All critical commercial language fixed
- [ ] 4 operational docs created
- [ ] Website structure understood

### Week 2 Gate
- [ ] Benefits framework transformed
- [ ] Strategic messaging guide created
- [ ] Vision documents updated with philosophical framing
- [ ] Core website pages complete
- [ ] Technical pipeline working

### Month 1 Gate
- [ ] Website fully deployed
- [ ] All messaging aligned
- [ ] Round 1 insights integrated to Canonical Library
- [ ] Research gaps documented
- [ ] Technical proposals evaluated
- [ ] Round 2 integrated/archived

## 🚦 Risk Mitigation

### If Delayed
1. **Commercial language**: Every day increases legal risk
2. **Website content**: Blocks launch and fundraising
3. **Operational docs**: Delays institutional adoption

### Mitigation Strategies
- Start with highest impact/lowest effort
- Run parallel tracks where possible
- Clear handoffs between work streams
- Daily progress checks on critical path

## 📋 Task Routing

### By Skill Set
- **Content/Writing**: Commercial audit, benefits, website content
- **Technical**: Pipeline setup, deployment, integration
- **Research/Analysis**: Operational docs, research audit
- **Strategic**: Messaging guide, cross-project coordination

### By Time Available
- **1 hour**: Quick wins, doc review
- **Half day**: Operational doc creation, benefits work
- **Full day**: Website content, complete integrations
- **Week**: Full Round 2 completion

---

## Next Immediate Action

**START HERE**: 
1. **FIRST**: Quarantine contaminated website content (30 min) - prevents accidental use
2. **THEN**: Execute commercial language quick wins (1 hour) AND Canonical Library reorganization (1 hour) in parallel
3. **WHILE**: Another team member reviews operational documentation prompts (1 hour)

This order ensures:
- Contaminated content isolated immediately
- Highest risks addressed first
- Single source of truth established early
- Dependencies respected
- Parallel work maximized
- Clear completion gates
- No rework needed