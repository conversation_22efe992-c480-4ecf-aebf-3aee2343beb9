# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview
EverArchive is a civilizational memory infrastructure project that captures creative process through the Deep Authorship 3-layer model (Core/Process/Surface). This is an Obsidian vault containing documentation, research, AI conversations, and implementation plans.

## Key Commands

### Obsidian Operations
- Open vault: Use Obsidian app or VS Code with Foam extension
- Search across vault: Use Obsidian's built-in search or `grep -r "search term" .`
- Find duplicates: `find . -name "*.md" -exec basename {} \; | sort | uniq -d`

### Git Operations
- View recent changes: `git log --oneline -10`
- Check file history: `git log --follow path/to/file.md`
- Find when file was added: `git log --diff-filter=A -- path/to/file.md`

### Content Analysis
- Count files by directory: `find . -name "*.md" | cut -d/ -f2 | sort | uniq -c`
- Find large files: `find . -name "*.md" -size +50k`
- List empty files: `find . -name "*.md" -size 0`

## Architecture & Structure

### Content Organization
The project has multiple parallel organizational structures that need consolidation:

1. **📚 Canonical Library/** - Primary documentation organized into 4 Tomes
   - Tome I: Vision documents
   - Tome II: Architecture specifications
   - Tome III: Operations manuals
   - Tome IV: Implementation details

2. **Centering the Archive/** - Alternative organization with EA-prefixed files
3. **📦 _archive/** - Contains both old versions and some current content
4. **Research/** - Multiple rounds of decentralized storage research

### Key Concepts
- **Deep Authorship Model**: 3-layer framework (Core/Process/Surface) for preserving creative process
- **Creator Sovereignty**: Zero-knowledge encryption ensuring creator control
- **Distributed Storage**: Non-extractive, permanent storage infrastructure
- **Forever Sites**: Previous project name (historical context)

### Important Files
- `/README.md` - Project overview and contribution guidelines
- `/📚 Canonical Library/Weighted Insights & Top-Priority Signals.md` - Key user insights
- Multiple versions of "Deep Authorship Manifesto" need consolidation
- Whitepaper exists only as Draft 1

## Known Issues & Gaps

1. **Missing Directories** referenced in README:
   - `/CHANGELOG/` - Should contain commit logs
   - `/scripts/` - Should contain automation helpers
   - `/docs/` structure doesn't match README specification

2. **Duplicate Content**:
   - Deep Authorship Manifesto (5+ versions)
   - Technical Architecture documents (4 versions)
   - Research documents in multiple locations

3. **Incomplete Files**:
   - `/xx-temp/List of Delivered Documents & Artifacts.md` is empty
   - `/📚 Canonical Library/Untitled.md` needs proper naming

## Development Workflow

### When Adding Content
1. Check for existing versions before creating new files
2. Use the Canonical Library for final versions
3. Place work-in-progress in appropriate directories
4. Update cross-references when moving/renaming files

### When Consolidating
1. Identify all versions using: `find . -name "*filename*" -type f`
2. Compare versions for differences: `diff file1.md file2.md`
3. Preserve version history in git before deleting duplicates
4. Update any internal links after consolidation

### When Preparing for Presentation
1. Focus on files in `/📚 Canonical Library/`
2. Reference weighted insights for key talking points
3. Use the 4 Tomes structure for logical flow
4. Check for complete versions of core documents

## AI Collaboration Context
This project extensively uses AI assistants for documentation and synthesis. When working with AI-generated content:
- Preserve original conversation logs in their current locations
- Synthesized versions should go in Canonical Library
- Maintain attribution to specific AI models when relevant
- Consider the multi-round synthesis approach used throughout

## MANDATORY AI AGENT REQUIREMENTS

### Core Thinking Process
- **MUST engage in thorough, systematic reasoning before EVERY response**
- Demonstrate careful analysis and consideration of multiple angles
- Break down complex problems into components
- Challenge assumptions and verify logic
- Show authentic curiosity and intellectual depth
- Consider edge cases and potential issues
- Never skip or shortcut the thinking process

### Thinking Format Requirements
- All reasoning must be in code blocks with `thinking` header
- Use natural, unstructured thought process
- No nested code blocks within thinking sections
- Show progressive understanding and development of ideas

### Thought Quality Standards
1. **Depth**
   - Explore multiple approaches and perspectives
   - Draw connections between ideas
   - Consider broader implications
   - Question initial assumptions

2. **Rigor**
   - Verify logical consistency
   - Fact-check when possible
   - Acknowledge limitations
   - Test conclusions

3. **Clarity**
   - Organize thoughts coherently
   - Break down complex ideas
   - Show reasoning progression
   - Connect thoughts to conclusions

### Technical Communication Standards
- When discussing technical topics, explain clearly and in depth for knowledgeable computer scientists
- When writing non-trivial code:
  - Think carefully, step-by-step, consider multiple approaches
  - Make a detailed plan before coding
  - Write detailed, helpful comments using lowercase letters
- Follow enterprise-grade coding standards

### Academic Scholar Mindset
Approach all work as an academic scholar writing white papers and building first-class systems for enterprise use at global scale:

**Academic Rigor:**
- Rigorous methodology and systematic analysis
- Evidence-based reasoning with proper documentation
- Comprehensive coverage leaving no stone unturned
- Clear definitions and precise terminology
- Logical flow building from foundations to conclusions

**Enterprise-Grade System Thinking:**
- Scalability from day one to millions of users
- Integration pathways with existing institutional systems
- Governance models that work at global scale
- Performance metrics and SLAs institutions expect
- Security and compliance as foundational requirements
- Risk mitigation and contingency planning

**Global Scale Perspective:**
- Cross-jurisdictional legal frameworks
- Cultural preservation across civilizations
- Multi-language and localization considerations
- Distributed infrastructure spanning continents
- Standards that work for diverse institutional needs

### Project Context
Treat EverArchive as critical infrastructure that major universities, national archives, and cultural institutions worldwide would stake their reputations on. This is not a startup project but civilizational memory infrastructure.

### Response Standards
- Clear and well-structured
- Thorough but accessible
- Professional while friendly
- Based on careful reasoning
- No emotes or casual language