/* PDF Generation Styles for EverArchive Whitepaper */

/* Page setup */
@page {
    size: A4;
    margin: 2.5cm 2cm 2.5cm 2cm;
    @bottom-center {
        content: counter(page);
        font-size: 10pt;
        color: #666;
    }
}

/* First page */
@page:first {
    @bottom-center {
        content: "";
    }
}

/* General typography */
body {
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
    font-size: 11pt;
    line-height: 1.6;
    color: #333;
    text-align: justify;
    hyphens: auto;
}

/* Headers */
h1, h2, h3, h4, h5, h6 {
    font-weight: 600;
    line-height: 1.3;
    margin-top: 1.5em;
    margin-bottom: 0.5em;
    text-align: left;
    hyphens: none;
}

h1 {
    font-size: 24pt;
    color: #1a1a1a;
    margin-top: 0;
    padding-top: 0;
}

h2 {
    font-size: 18pt;
    color: #2a2a2a;
    page-break-before: always;
    margin-top: 0;
    padding-top: 0;
}

/* Exception: Don't break before h2 inside table of contents */
#table-of-contents + h2,
h1 + h2 {
    page-break-before: avoid;
}

h3 {
    font-size: 14pt;
    color: #3a3a3a;
    page-break-after: avoid;
}

h4 {
    font-size: 12pt;
    color: #4a4a4a;
    page-break-after: avoid;
}

/* Paragraphs */
p {
    margin: 0 0 0.8em 0;
    orphans: 3;
    widows: 3;
}

/* Lists */
ul, ol {
    margin: 0.5em 0 0.8em 1.5em;
    padding: 0;
}

li {
    margin-bottom: 0.3em;
}

/* Code blocks */
pre {
    background-color: #f5f5f5;
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 1em;
    font-size: 9pt;
    overflow-x: auto;
    page-break-inside: avoid;
}

code {
    font-family: "SF Mono", Monaco, "Courier New", monospace;
    font-size: 9pt;
    background-color: #f5f5f5;
    padding: 0.1em 0.3em;
    border-radius: 3px;
}

pre code {
    background-color: transparent;
    padding: 0;
}

/* Blockquotes */
blockquote {
    margin: 1em 0;
    padding-left: 1em;
    border-left: 4px solid #ddd;
    color: #666;
    font-style: italic;
}

/* Tables */
table {
    width: 100%;
    border-collapse: collapse;
    margin: 1em 0;
    page-break-inside: avoid;
}

th, td {
    border: 1px solid #ddd;
    padding: 0.5em;
    text-align: left;
}

th {
    background-color: #f5f5f5;
    font-weight: 600;
}

/* Links */
a {
    color: #0066cc;
    text-decoration: none;
}

a:hover {
    text-decoration: underline;
}

/* Internal links styling */
a[href^="#"] {
    color: #0066cc;
}

/* Horizontal rules */
hr {
    border: none;
    border-top: 1px solid #ccc;
    margin: 2em 0;
    page-break-after: avoid;
}

/* Strong and emphasis */
strong {
    font-weight: 600;
}

em {
    font-style: italic;
}

/* Page break utilities */
.page-break {
    page-break-after: always;
}

.no-page-break {
    page-break-inside: avoid;
}

/* Table of contents specific styling */
#table-of-contents {
    page-break-after: always;
}

#table-of-contents ol {
    list-style-type: decimal;
    margin-left: 0;
}

#table-of-contents li {
    margin-bottom: 0.5em;
}

#table-of-contents a {
    text-decoration: none;
    color: #333;
}

#table-of-contents a:hover {
    color: #0066cc;
}

/* Executive summary box */
#executive-summary {
    background-color: #f9f9f9;
    border: 1px solid #e0e0e0;
    padding: 1.5em;
    margin: 1em 0;
    border-radius: 8px;
}

/* Note boxes */
.note {
    background-color: #fff9e6;
    border-left: 4px solid #ffcc00;
    padding: 1em;
    margin: 1em 0;
}

/* Placeholder styling */
[placeholder] {
    background-color: #ffe6e6;
    padding: 0.2em 0.4em;
    border-radius: 3px;
}

/* Appendices */
#appendices {
    page-break-before: always;
}

/* Call to action section */
#call-to-action {
    page-break-before: always;
}

/* Keep related content together */
h3 + p,
h4 + p,
h3 + ul,
h4 + ul {
    page-break-before: avoid;
}

/* Diagram/ASCII art blocks */
pre:has(+ pre) {
    page-break-after: avoid;
}

/* Final adjustments for better readability */
@media print {
    body {
        font-size: 10.5pt;
    }
    
    h1 {
        font-size: 22pt;
    }
    
    h2 {
        font-size: 16pt;
    }
    
    h3 {
        font-size: 13pt;
    }
    
    h4 {
        font-size: 11pt;
    }
}