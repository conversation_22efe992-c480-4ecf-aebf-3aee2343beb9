#!/usr/bin/env python3
"""
Create PDF from HTML on macOS using WebKit
This script uses macOS's WebKit to render HTML and save as PDF
"""

import os
import sys
import subprocess
import tempfile
from pathlib import Path

def create_pdf_from_html_macos(html_file, pdf_file):
    """
    Use AppleScript to print HTML to PDF on macOS
    """
    applescript = f'''
    tell application "Safari"
        activate
        open POSIX file "{os.path.abspath(html_file)}"
        delay 3
        
        tell application "System Events"
            keystroke "p" using command down
            delay 2
            
            -- Click the PDF button
            click menu button "PDF" of sheet 1 of window 1 of application process "Safari"
            delay 0.5
            
            -- Select "Save as PDF"
            click menu item "Save as PDF" of menu 1 of menu button "PDF" of sheet 1 of window 1 of application process "Safari"
            delay 1
            
            -- Type the filename
            keystroke "{os.path.basename(pdf_file)}"
            
            -- Save
            click button "Save" of sheet 1 of window 1 of application process "Safari"
            delay 2
        end tell
        
        -- Close the tab
        close current tab of window 1
    end tell
    '''
    
    # Write AppleScript to temporary file
    with tempfile.NamedTemporaryFile(mode='w', suffix='.scpt', delete=False) as f:
        f.write(applescript)
        script_path = f.name
    
    try:
        # Execute AppleScript
        subprocess.run(['osascript', script_path], check=True)
        print(f"PDF created: {pdf_file}")
    except subprocess.CalledProcessError as e:
        print(f"Error creating PDF: {e}")
    finally:
        # Clean up
        os.unlink(script_path)

def create_pdf_with_webkit():
    """
    Alternative method using WebKit directly
    """
    swift_code = '''
    import WebKit
    import Foundation
    
    let html = try! String(contentsOfFile: CommandLine.arguments[1])
    let outputPath = CommandLine.arguments[2]
    
    let webView = WKWebView(frame: CGRect(x: 0, y: 0, width: 595, height: 842))
    webView.loadHTMLString(html, baseURL: URL(fileURLWithPath: FileManager.default.currentDirectoryPath))
    
    DispatchQueue.main.asyncAfter(deadline: .now() + 2.0) {
        let printInfo = NSPrintInfo()
        printInfo.paperSize = NSSize(width: 595, height: 842) // A4
        printInfo.topMargin = 72
        printInfo.bottomMargin = 72
        printInfo.leftMargin = 72
        printInfo.rightMargin = 72
        
        let printOp = NSPrintOperation(view: webView, printInfo: printInfo)
        printOp.showsPrintPanel = false
        printOp.showsProgressPanel = false
        
        if let pdfData = printOp.pdfData {
            try! pdfData.write(to: URL(fileURLWithPath: outputPath))
            print("PDF created successfully")
            exit(0)
        } else {
            print("Failed to create PDF")
            exit(1)
        }
    }
    
    RunLoop.main.run()
    '''
    
    # This would require compiling Swift code, which is complex
    # Providing as reference for a more robust solution
    pass

if __name__ == "__main__":
    html_file = "EverArchive-Whitepaper-v3.0.html"
    pdf_file = "EverArchive-Whitepaper-v3.0.pdf"
    
    if not os.path.exists(html_file):
        print(f"Error: {html_file} not found")
        print("Please run generate-pdf.sh first to create the HTML file")
        sys.exit(1)
    
    print("Creating PDF from HTML...")
    print("Note: This will open Safari briefly to generate the PDF")
    print("Please allow the script to control Safari if prompted")
    
    # For now, provide instructions since automated PDF creation requires additional tools
    print("\nManual steps to create PDF:")
    print(f"1. Open {html_file} in Safari or Chrome")
    print("2. Press Cmd+P to open the print dialog")
    print("3. Click 'PDF' dropdown and select 'Save as PDF'")
    print("4. Save as 'EverArchive-Whitepaper-v3.0.pdf'")
    print("\nThe document has been formatted with:")
    print("- Page breaks before each major section")
    print("- Working internal links")
    print("- Professional typography")
    print("- Proper margins and spacing")