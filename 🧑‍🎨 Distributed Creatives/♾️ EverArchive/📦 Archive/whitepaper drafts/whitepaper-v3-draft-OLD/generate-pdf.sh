#!/bin/bash

# Generate HTML with embedded CSS for PDF printing
pandoc whitepaper-v3.0-final.md \
  -o EverArchive-Whitepaper-v3.0.html \
  --standalone \
  --embed-resources \
  --toc \
  --toc-depth=2 \
  --lua-filter=pagebreak.lua \
  --metadata title="EverArchive: Building Civilizational Memory Infrastructure" \
  --metadata date="June 2025" \
  --metadata author="EverArchive Foundation" \
  --css=whitepaper-pdf-style.css \
  --template=default.html5

echo "HTML file generated: EverArchive-Whitepaper-v3.0.html"
echo ""
echo "To create a PDF:"
echo "1. Open EverArchive-Whitepaper-v3.0.html in your web browser"
echo "2. Press Cmd+P (Mac) or Ctrl+P (Windows/Linux) to print"
echo "3. Select 'Save as PDF' as the destination"
echo "4. Make sure to:"
echo "   - Enable background graphics"
echo "   - Set margins to 'Default' or 'Minimum'"
echo "   - Choose A4 or Letter paper size"
echo ""
echo "The CSS has been configured to:"
echo "- Start each major section (h2) on a new page"
echo "- Preserve internal links"
echo "- Format the document professionally"