<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml" lang="en" xml:lang="en">
<head>
  <meta charset="utf-8" />
  <meta name="generator" content="pandoc" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=yes" />
  <meta name="author" content="EverArchive Foundation" />
  <meta name="date" content="June 2025" />
  <title>$title$</title>
  <style>
    /* PDF Generation Styles for EverArchive Whitepaper */
    
    /* Reset and base styles */
    * {
      box-sizing: border-box;
    }
    
    /* Page setup for printing */
    @page {
      size: A4;
      margin: 2.5cm 2cm 2.5cm 2cm;
    }
    
    @page :first {
      margin-top: 0;
    }
    
    /* General typography */
    body {
      font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", <PERSON><PERSON>, "Helvetica Neue", Arial, sans-serif;
      font-size: 11pt;
      line-height: 1.6;
      color: #333;
      max-width: 210mm;
      margin: 0 auto;
      padding: 0;
    }
    
    /* Headers with page breaks */
    h1, h2, h3, h4, h5, h6 {
      font-weight: 600;
      line-height: 1.3;
      margin-top: 1.5em;
      margin-bottom: 0.5em;
      page-break-after: avoid;
    }
    
    h1 {
      font-size: 28pt;
      color: #1a1a1a;
      text-align: center;
      margin-top: 0;
      padding-top: 0;
      page-break-before: avoid;
    }
    
    h2 {
      font-size: 20pt;
      color: #2a2a2a;
      page-break-before: always;
      margin-top: 0;
      padding-top: 0;
    }
    
    /* Don't break before first h2 or h2 right after h1 */
    h1 + h2,
    body > h2:first-of-type,
    #table-of-contents + h2 {
      page-break-before: avoid;
    }
    
    h3 {
      font-size: 16pt;
      color: #3a3a3a;
    }
    
    h4 {
      font-size: 14pt;
      color: #4a4a4a;
    }
    
    /* Paragraphs */
    p {
      margin: 0 0 1em 0;
      text-align: justify;
      orphans: 3;
      widows: 3;
    }
    
    /* Lists */
    ul, ol {
      margin: 0.5em 0 1em 1.5em;
      padding: 0;
    }
    
    li {
      margin-bottom: 0.3em;
    }
    
    /* Links - ensure they work in PDF */
    a {
      color: #0066cc;
      text-decoration: none;
    }
    
    a[href^="#"] {
      color: #0066cc;
    }
    
    /* Code blocks */
    pre {
      background-color: #f5f5f5;
      border: 1px solid #ddd;
      border-radius: 4px;
      padding: 1em;
      font-size: 9pt;
      overflow-x: auto;
      page-break-inside: avoid;
      white-space: pre-wrap;
      word-wrap: break-word;
    }
    
    code {
      font-family: "SF Mono", Monaco, "Courier New", monospace;
      font-size: 9pt;
      background-color: #f5f5f5;
      padding: 0.1em 0.3em;
      border-radius: 3px;
    }
    
    pre code {
      background-color: transparent;
      padding: 0;
    }
    
    /* Tables */
    table {
      width: 100%;
      border-collapse: collapse;
      margin: 1em 0;
      page-break-inside: avoid;
    }
    
    th, td {
      border: 1px solid #ddd;
      padding: 0.5em;
      text-align: left;
    }
    
    th {
      background-color: #f5f5f5;
      font-weight: 600;
    }
    
    /* Blockquotes */
    blockquote {
      margin: 1em 0;
      padding-left: 1em;
      border-left: 4px solid #ddd;
      color: #666;
      font-style: italic;
    }
    
    /* Table of contents */
    #table-of-contents {
      page-break-after: always;
    }
    
    nav#TOC {
      page-break-after: always;
      margin-bottom: 2em;
    }
    
    nav#TOC ul {
      list-style-type: decimal;
      margin-left: 0;
    }
    
    nav#TOC li {
      margin-bottom: 0.5em;
    }
    
    nav#TOC a {
      text-decoration: none;
      color: #333;
    }
    
    nav#TOC a:hover {
      color: #0066cc;
    }
    
    /* Print-specific adjustments */
    @media print {
      body {
        font-size: 10pt;
      }
      
      h1 {
        font-size: 24pt;
      }
      
      h2 {
        font-size: 18pt;
      }
      
      h3 {
        font-size: 14pt;
      }
      
      h4 {
        font-size: 12pt;
      }
      
      /* Ensure links are visible */
      a[href^="http"]:after {
        content: "";
      }
      
      /* Keep related content together */
      h1, h2, h3, h4 {
        page-break-after: avoid;
      }
      
      p {
        orphans: 3;
        widows: 3;
      }
      
      /* Page break utilities */
      .page-break-before {
        page-break-before: always;
      }
      
      .page-break-after {
        page-break-after: always;
      }
      
      .no-page-break {
        page-break-inside: avoid;
      }
    }
    
    /* Specific section styling */
    #executive-summary {
      background-color: #f9f9f9;
      border: 1px solid #e0e0e0;
      padding: 1.5em;
      margin: 1em 0;
      border-radius: 8px;
      page-break-inside: avoid;
    }
    
    /* Horizontal rules */
    hr {
      border: none;
      border-top: 1px solid #ccc;
      margin: 2em 0;
      page-break-after: avoid;
    }
  </style>
  $for(css)$
  <link rel="stylesheet" href="$css$" />
  $endfor$
</head>
<body>
$if(title)$
<header id="title-block-header">
<h1 class="title">$title$</h1>
$if(author)$
<p class="author">$author$</p>
$endif$
$if(date)$
<p class="date">$date$</p>
$endif$
</header>
$endif$
$if(toc)$
<nav id="$idprefix$TOC" role="doc-toc">
$if(toc-title)$
<h2 id="$idprefix$toc-title">$toc-title$</h2>
$endif$
$table-of-contents$
</nav>
$endif$
$body$
</body>
</html>